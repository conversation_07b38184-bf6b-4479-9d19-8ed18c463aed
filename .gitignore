# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
.idea/
dist
# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.vscode
# testing
/coverage
dump.rdb
chainlit.md
.chainlit
# next.js
/.next/
/out/
.ipynb_checkpoints/
# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
# .env*.local
*token.json*
# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
*pyc*
#*.env
.ac
agenthub/agents/youtube/db
*credentials.json*

venv/
.venv/
__pycache__/