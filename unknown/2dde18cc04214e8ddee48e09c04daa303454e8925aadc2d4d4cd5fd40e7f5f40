// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Skills Component renders correctly 1`] = `
<div>
  <section
    class="bg-white py-16 dark:bg-gray-800"
    id="skills"
  >
    <div
      class="container mx-auto px-4"
    >
      <h2
        class="mb-2 text-center text-3xl font-bold tracking-tight text-gray-800 dark:text-white"
      >
        Skills & Expertise
      </h2>
      <p
        class="mx-auto mb-10 max-w-2xl text-center font-light tracking-wide text-gray-600 dark:text-gray-300"
      >
        Professional competencies across AI, product, and technical domains
      </p>
      <div
        class="mb-10 flex flex-wrap justify-center gap-3"
      >
        <button
          class="rounded-full px-5 py-2.5 text-sm font-medium shadow-sm transition-all duration-300 md:text-base bg-blue-600 text-white shadow-md"
          data-testid="motion-button"
          whilehover="[object Object]"
          whiletap="[object Object]"
        >
          All Skills
        </button>
        <button
          class="rounded-full px-5 py-2.5 text-sm font-medium shadow-sm transition-all duration-300 md:text-base bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          data-testid="motion-button"
          style="border-left: 3px solid #3B82F6;"
          whilehover="[object Object]"
          whiletap="[object Object]"
        >
          AI/ML & Product
        </button>
        <button
          class="rounded-full px-5 py-2.5 text-sm font-medium shadow-sm transition-all duration-300 md:text-base bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          data-testid="motion-button"
          style="border-left: 3px solid #10B981;"
          whilehover="[object Object]"
          whiletap="[object Object]"
        >
          Technical Skills
        </button>
        <button
          class="rounded-full px-5 py-2.5 text-sm font-medium shadow-sm transition-all duration-300 md:text-base bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
          data-testid="motion-button"
          style="border-left: 3px solid #F59E0B;"
          whilehover="[object Object]"
          whiletap="[object Object]"
        >
          Leadership & Strategy
        </button>
      </div>
      <div
        data-testid="animate-presence"
      />
      <div
        class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3"
      >
        <div
          data-testid="animate-presence"
        >
          <div
            animate="[object Object]"
            class="skill-card overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800"
            data-skill-id="skill-1"
            data-testid="motion-div"
            exit="[object Object]"
            initial="[object Object]"
            transition="[object Object]"
            whilehover="[object Object]"
          >
            <div
              class="h-2"
              style="background-color: rgb(16, 185, 129);"
            />
            <div
              class="p-5"
            >
              <div
                class="mb-3 flex items-start justify-between"
              >
                <h3
                  class="text-lg font-semibold text-gray-800 dark:text-white"
                >
                  Test Skill 1
                </h3>
                <span
                  class="rounded-full px-2 py-1 text-xs font-medium"
                  style="background-color: rgba(59, 130, 246, 0.125); color: rgb(59, 130, 246);"
                >
                  Expert
                </span>
              </div>
              <div
                class="relative mt-4"
              >
                <div
                  class="mb-1 flex items-center justify-between space-x-3"
                >
                  <span
                    class="text-xs font-medium tracking-wide text-gray-500 dark:text-gray-400"
                  >
                    Proficiency
                  </span>
                  <span
                    class="text-lg font-bold"
                    style="color: rgb(16, 185, 129);"
                  >
                    95
                    %
                  </span>
                </div>
                <div
                  class="h-2.5 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700"
                >
                  <div
                    animate="[object Object]"
                    class="h-full rounded-full"
                    data-testid="motion-div"
                    style="width: 0%; background-color: rgb(16, 185, 129);"
                    transition="[object Object]"
                  />
                </div>
              </div>
              <div
                class="mt-4"
              >
                <span
                  class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600 dark:bg-gray-700 dark:text-gray-300"
                >
                  Technical Skills
                </span>
              </div>
            </div>
          </div>
          <div
            animate="[object Object]"
            class="skill-card overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800"
            data-skill-id="skill-2"
            data-testid="motion-div"
            exit="[object Object]"
            initial="[object Object]"
            transition="[object Object]"
            whilehover="[object Object]"
          >
            <div
              class="h-2"
              style="background-color: rgb(16, 185, 129);"
            />
            <div
              class="p-5"
            >
              <div
                class="mb-3 flex items-start justify-between"
              >
                <h3
                  class="text-lg font-semibold text-gray-800 dark:text-white"
                >
                  Test Skill 2
                </h3>
                <span
                  class="rounded-full px-2 py-1 text-xs font-medium"
                  style="background-color: rgba(16, 185, 129, 0.125); color: rgb(16, 185, 129);"
                >
                  Advanced
                </span>
              </div>
              <div
                class="relative mt-4"
              >
                <div
                  class="mb-1 flex items-center justify-between space-x-3"
                >
                  <span
                    class="text-xs font-medium tracking-wide text-gray-500 dark:text-gray-400"
                  >
                    Proficiency
                  </span>
                  <span
                    class="text-lg font-bold"
                    style="color: rgb(16, 185, 129);"
                  >
                    85
                    %
                  </span>
                </div>
                <div
                  class="h-2.5 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700"
                >
                  <div
                    animate="[object Object]"
                    class="h-full rounded-full"
                    data-testid="motion-div"
                    style="width: 0%; background-color: rgb(16, 185, 129);"
                    transition="[object Object]"
                  />
                </div>
              </div>
              <div
                class="mt-4"
              >
                <span
                  class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600 dark:bg-gray-700 dark:text-gray-300"
                >
                  Technical Skills
                </span>
              </div>
            </div>
          </div>
          <div
            animate="[object Object]"
            class="skill-card overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-all duration-300 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800"
            data-skill-id="skill-3"
            data-testid="motion-div"
            exit="[object Object]"
            initial="[object Object]"
            transition="[object Object]"
            whilehover="[object Object]"
          >
            <div
              class="h-2"
              style="background-color: rgb(16, 185, 129);"
            />
            <div
              class="p-5"
            >
              <div
                class="mb-3 flex items-start justify-between"
              >
                <h3
                  class="text-lg font-semibold text-gray-800 dark:text-white"
                >
                  Test Skill 3
                </h3>
                <span
                  class="rounded-full px-2 py-1 text-xs font-medium"
                  style="background-color: rgba(245, 158, 11, 0.125); color: rgb(245, 158, 11);"
                >
                  Intermediate
                </span>
              </div>
              <div
                class="relative mt-4"
              >
                <div
                  class="mb-1 flex items-center justify-between space-x-3"
                >
                  <span
                    class="text-xs font-medium tracking-wide text-gray-500 dark:text-gray-400"
                  >
                    Proficiency
                  </span>
                  <span
                    class="text-lg font-bold"
                    style="color: rgb(16, 185, 129);"
                  >
                    75
                    %
                  </span>
                </div>
                <div
                  class="h-2.5 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700"
                >
                  <div
                    animate="[object Object]"
                    class="h-full rounded-full"
                    data-testid="motion-div"
                    style="width: 0%; background-color: rgb(16, 185, 129);"
                    transition="[object Object]"
                  />
                </div>
              </div>
              <div
                class="mt-4"
              >
                <span
                  class="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600 dark:bg-gray-700 dark:text-gray-300"
                >
                  Technical Skills
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
`;
