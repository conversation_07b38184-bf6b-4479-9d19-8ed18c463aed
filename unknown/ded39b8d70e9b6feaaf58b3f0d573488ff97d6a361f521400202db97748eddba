// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ThemeToggle Component renders correctly 1`] = `
<div>
  <button
    animate="[object Object]"
    aria-label="Switch to dark mode"
    class="relative flex h-11 w-11 items-center justify-center rounded-full bg-white/10 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/20 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 active:scale-95"
    data-testid="motion-button"
    transition="[object Object]"
    type="button"
    whilehover="[object Object]"
    whiletap="[object Object]"
  >
    <div
      animate="[object Object]"
      class="absolute inset-0 flex items-center justify-center"
      data-testid="motion-div"
      transition="[object Object]"
    >
      <div
        data-testid="sun-icon"
      >
        Sun Icon
      </div>
    </div>
    <div
      animate="[object Object]"
      class="absolute inset-0 flex items-center justify-center"
      data-testid="motion-div"
      transition="[object Object]"
    >
      <div
        data-testid="moon-icon"
      >
        Moon Icon
      </div>
    </div>
  </button>
</div>
`;
