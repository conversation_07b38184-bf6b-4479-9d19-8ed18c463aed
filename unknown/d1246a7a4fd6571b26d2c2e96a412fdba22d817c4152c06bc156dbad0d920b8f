// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FloatingActionButton Component renders correctly 1`] = `
<div>
  <div
    class="fixed bottom-6 right-6 z-40"
  >
    <div
      class="absolute bottom-full right-0 mb-4 rounded-lg bg-white px-3 py-2 text-sm text-gray-700 shadow-md dark:bg-gray-800 dark:text-gray-200"
      data-testid="motion-div"
    >
      Scroll to timeline
    </div>
    <button
      aria-label="Scroll to timeline"
      class="flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transition-colors duration-300 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-gray-900"
      data-testid="motion-button"
    >
      <div
        data-testid="chevron-down-icon"
      >
        ChevronDown Icon
      </div>
    </button>
  </div>
</div>
`;
