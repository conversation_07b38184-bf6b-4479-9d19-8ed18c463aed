// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Header Component renders correctly 1`] = `
<div>
  <div
    class="relative flex h-screen items-center justify-center overflow-hidden pb-20 pt-[var(--space-section)] md:pb-32"
    data-testid="parallax-banner"
    id="top"
    layers="[object Object]"
  >
    <div
      animate="[object Object]"
      class="absolute right-4 top-4 z-20 md:right-6 md:top-6"
      data-testid="motion-div"
      initial="[object Object]"
      transition="[object Object]"
    >
      <button
        animate="[object Object]"
        aria-label="Switch to dark mode"
        class="relative flex h-11 w-11 items-center justify-center rounded-full bg-white/10 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/20 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 active:scale-95"
        data-testid="motion-button"
        transition="[object Object]"
        type="button"
        whilehover="[object Object]"
        whiletap="[object Object]"
      >
        <div
          animate="[object Object]"
          class="absolute inset-0 flex items-center justify-center"
          data-testid="motion-div"
          transition="[object Object]"
        >
          <div
            data-testid="sun-icon"
          >
            Sun Icon
          </div>
        </div>
        <div
          animate="[object Object]"
          class="absolute inset-0 flex items-center justify-center"
          data-testid="motion-div"
          transition="[object Object]"
        >
          <div
            data-testid="moon-icon"
          >
            Moon Icon
          </div>
        </div>
      </button>
    </div>
    <div
      class="container relative z-10 mx-auto px-6 py-8"
    >
      <div
        class="flex h-full flex-col items-center justify-center text-center"
      >
        <div
          animate="[object Object]"
          class="mb-8 max-w-xl text-center text-white"
          data-testid="motion-div"
          initial="[object Object]"
          transition="[object Object]"
        >
          <h1
            animate="[object Object]"
            class="mb-6 font-sans text-5xl font-bold tracking-tight md:text-6xl"
            data-testid="motion-h1"
            initial="[object Object]"
            transition="[object Object]"
          >
            Test User
          </h1>
          <div
            animate="[object Object]"
            data-testid="motion-div"
            initial="[object Object]"
            transition="[object Object]"
          >
            <p
              class="text-2xl font-medium text-blue-300 md:text-3xl"
            >
              Test Bio
            </p>
          </div>
        </div>
        <div
          class="w-full max-w-md"
        >
          <div
            animate="[object Object]"
            class="relative w-full"
            data-testid="motion-div"
            initial="[object Object]"
            transition="[object Object]"
          >
            <div
              aria-keyshortcuts="⌘+K"
              aria-label="Open command palette"
              class="flex cursor-pointer items-center rounded-full bg-white/10 px-4 py-3 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/15 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75"
              data-testid="motion-div"
              whilehover="[object Object]"
              whiletap="[object Object]"
            >
              <div
                data-testid="command-icon"
              >
                Command Icon
              </div>
              <span
                class="flex-1 text-left text-white/70"
              >
                Find contact info or navigate...
              </span>
              <kbd
                class="hidden items-center justify-center rounded bg-white/5 px-2 py-1 text-xs text-white/70 md:flex"
              >
                Ctrl+K
              </kbd>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Header Component renders with command menu functionality 1`] = `
<div>
  <div
    class="relative flex h-screen items-center justify-center overflow-hidden pb-20 pt-[var(--space-section)] md:pb-32"
    data-testid="parallax-banner"
    id="top"
    layers="[object Object]"
  >
    <div
      animate="[object Object]"
      class="absolute right-4 top-4 z-20 md:right-6 md:top-6"
      data-testid="motion-div"
      initial="[object Object]"
      transition="[object Object]"
    >
      <button
        animate="[object Object]"
        aria-label="Switch to dark mode"
        class="relative flex h-11 w-11 items-center justify-center rounded-full bg-white/10 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/20 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 active:scale-95"
        data-testid="motion-button"
        transition="[object Object]"
        type="button"
        whilehover="[object Object]"
        whiletap="[object Object]"
      >
        <div
          animate="[object Object]"
          class="absolute inset-0 flex items-center justify-center"
          data-testid="motion-div"
          transition="[object Object]"
        >
          <div
            data-testid="sun-icon"
          >
            Sun Icon
          </div>
        </div>
        <div
          animate="[object Object]"
          class="absolute inset-0 flex items-center justify-center"
          data-testid="motion-div"
          transition="[object Object]"
        >
          <div
            data-testid="moon-icon"
          >
            Moon Icon
          </div>
        </div>
      </button>
    </div>
    <div
      class="container relative z-10 mx-auto px-6 py-8"
    >
      <div
        class="flex h-full flex-col items-center justify-center text-center"
      >
        <div
          animate="[object Object]"
          class="mb-8 max-w-xl text-center text-white"
          data-testid="motion-div"
          initial="[object Object]"
          transition="[object Object]"
        >
          <h1
            animate="[object Object]"
            class="mb-6 font-sans text-5xl font-bold tracking-tight md:text-6xl"
            data-testid="motion-h1"
            initial="[object Object]"
            transition="[object Object]"
          >
            Test User
          </h1>
          <div
            animate="[object Object]"
            data-testid="motion-div"
            initial="[object Object]"
            transition="[object Object]"
          >
            <p
              class="text-2xl font-medium text-blue-300 md:text-3xl"
            >
              Test Bio
            </p>
          </div>
        </div>
        <div
          class="w-full max-w-md"
        >
          <div
            animate="[object Object]"
            class="relative w-full"
            data-testid="motion-div"
            initial="[object Object]"
            transition="[object Object]"
          >
            <div
              aria-keyshortcuts="⌘+K"
              aria-label="Open command palette"
              class="flex cursor-pointer items-center rounded-full bg-white/10 px-4 py-3 text-white backdrop-blur-sm transition-all duration-200 hover:bg-white/15 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75"
              data-testid="motion-div"
              whilehover="[object Object]"
              whiletap="[object Object]"
            >
              <div
                data-testid="command-icon"
              >
                Command Icon
              </div>
              <span
                class="flex-1 text-left text-white/70"
              >
                Find contact info or navigate...
              </span>
              <kbd
                class="hidden items-center justify-center rounded bg-white/5 px-2 py-1 text-xs text-white/70 md:flex"
              >
                Ctrl+K
              </kbd>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
