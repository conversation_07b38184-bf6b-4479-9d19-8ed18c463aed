// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Timeline Component renders correctly with collapsed items 1`] = `
<div>
  <section
    class="bg-canvas py-[var(--space-section)] dark:bg-canvas-dark"
    id="timeline"
  >
    <div
      class="container mx-auto px-4"
    >
      <h2
        class="text-high-contrast mb-12 text-center text-3xl font-bold"
      >
        Professional Timeline
      </h2>
      <div
        class="timeline-grid-container relative mx-auto w-full max-w-none px-4 md:max-w-6xl md:px-8"
      >
        <div
          class="timeline-line-desktop hidden w-0.5 bg-gradient-to-b from-primary-400 via-primary-500 to-primary-600 md:block"
          style="height: 96px; margin-top: 46px;"
        />
        <div
          class="absolute w-0.5 bg-gradient-to-b from-primary-400 via-primary-500 to-primary-600 md:hidden"
          style="height: 96px; top: 46px; left: 22px;"
        />
        <div
          class="timeline-grid-item relative timeline-left"
          style="min-height: 96px; padding-top: 24px; padding-bottom: 16px; margin-bottom: 16px;"
        >
          <button
            aria-controls="timeline-content-exp-1"
            aria-describedby="timeline-content-exp-1"
            aria-expanded="false"
            aria-label="Expand details for Test Position at Test Company"
            aria-labelledby="timeline-title-exp-1"
            class="timeline-icon timeline-icon-focus flex h-11 min-h-[44px] w-11 min-w-[44px] cursor-pointer items-center justify-center rounded-full bg-primary-500 text-white shadow-lg hover:scale-110 hover:bg-primary-600 hover:shadow-xl active:scale-95 animate-timeline-pulse transition-all duration-300"
            role="button"
            type="button"
          >
            <div
              data-testid="brain-icon"
            >
              Brain
            </div>
          </button>
          <div
            animate="{\\"transition\\":{\\"duration\\":0.4,\\"ease\\":[0.4,0,0.2,1],\\"type\\":\\"spring\\",\\"stiffness\\":300,\\"damping\\":30}}"
            class="timeline-card-wrapper relative w-full md:w-auto z-timeline-card md:max-w-sm lg:max-w-md xl:max-w-lg "
            data-testid="motion-div"
            id="timeline-content-exp-1"
            layout="\\"position\\""
            style="margin-top: 0px;"
          >
            <div
              class="timeline-year-label-above"
            >
              <p
                class="whitespace-nowrap text-lg font-semibold text-primary-600 transition-all duration-300 dark:text-primary-400 md:text-xl"
              >
                2020-Present
              </p>
            </div>
            <div
              aria-controls="timeline-expanded-exp-1"
              aria-labelledby="timeline-title-exp-1"
              class="timeline-card rounded-lg bg-white/95 shadow-md backdrop-blur-sm dark:bg-slate-800/95 timeline-card-hover transition-all duration-300 text-left p-4 timeline-card-focus relative cursor-pointer"
              data-testid="motion-div"
              data-view-transition-name="timeline-card-1"
              layout="\\"position\\""
              role="button"
              style="--view-transition-name: timeline-card-1;"
              tabindex="0"
            >
              <div
                data-testid="motion-div"
                layout="\\"position\\""
              >
                <h3
                  class="mb-2 text-lg text-high-contrast font-semibold leading-tight"
                  id="timeline-title-exp-1"
                >
                  Test Company
                </h3>
                <p
                  class="mb-2 text-base text-medium-contrast font-medium leading-snug"
                >
                  Test Position
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          class="timeline-grid-item relative timeline-right"
          style="min-height: 96px; padding-top: 24px; padding-bottom: 16px; margin-bottom: 16px;"
        >
          <button
            aria-controls="timeline-content-exp-2"
            aria-describedby="timeline-content-exp-2"
            aria-expanded="false"
            aria-label="Expand details for Previous Position at Previous Company"
            aria-labelledby="timeline-title-exp-2"
            class="timeline-icon timeline-icon-focus flex h-11 min-h-[44px] w-11 min-w-[44px] cursor-pointer items-center justify-center rounded-full bg-primary-500 text-white shadow-lg hover:scale-110 hover:bg-primary-600 hover:shadow-xl active:scale-95 animate-timeline-pulse transition-all duration-300"
            role="button"
            type="button"
          >
            <div
              data-testid="briefcase-icon"
            >
              Briefcase
            </div>
          </button>
          <div
            animate="{\\"transition\\":{\\"duration\\":0.4,\\"ease\\":[0.4,0,0.2,1],\\"type\\":\\"spring\\",\\"stiffness\\":300,\\"damping\\":30}}"
            class="timeline-card-wrapper relative w-full md:w-auto z-timeline-card md:max-w-sm lg:max-w-md xl:max-w-lg "
            data-testid="motion-div"
            id="timeline-content-exp-2"
            layout="\\"position\\""
            style="margin-top: 0px;"
          >
            <div
              class="timeline-year-label-above"
            >
              <p
                class="whitespace-nowrap text-lg font-semibold text-primary-600 transition-all duration-300 dark:text-primary-400 md:text-xl"
              >
                2018-2020
              </p>
            </div>
            <div
              aria-controls="timeline-expanded-exp-2"
              aria-labelledby="timeline-title-exp-2"
              class="timeline-card rounded-lg bg-white/95 shadow-md backdrop-blur-sm dark:bg-slate-800/95 timeline-card-hover transition-all duration-300 text-left p-4 timeline-card-focus relative cursor-pointer"
              data-testid="motion-div"
              data-view-transition-name="timeline-card-2"
              layout="\\"position\\""
              role="button"
              style="--view-transition-name: timeline-card-2;"
              tabindex="0"
            >
              <div
                data-testid="motion-div"
                layout="\\"position\\""
              >
                <h3
                  class="mb-2 text-lg text-high-contrast font-semibold leading-tight"
                  id="timeline-title-exp-2"
                >
                  Previous Company
                </h3>
                <p
                  class="mb-2 text-base text-medium-contrast font-medium leading-snug"
                >
                  Previous Position
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
`;
