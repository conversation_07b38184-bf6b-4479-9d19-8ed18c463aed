/* Import Space Grotesk font from Google Fonts - 2025's trending digital-friendly font */
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

/* Import design tokens */
@import './tokens.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ◀︎ LLM-modified: Removed legacy CSS variables - now using design tokens from tokens.css */
/* All styling now uses the comprehensive token system for consistency */

/* Smooth scrolling for the entire page, with reduced motion preference support */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-none-on-reduce-motion,
  .animate-gradient-shift,
  .animate-bounce-slow {
    animation: none !important;
    transition: none !important;
  }
}

body {
  @apply bg-canvas text-text dark:bg-canvas-dark dark:text-text-dark;
  font-family:
    'Space Grotesk',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Font weight utilities */
.font-thin {
  font-weight: 100;
}

.font-extralight {
  font-weight: 200;
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-black {
  font-weight: 900;
}

/* Focus styles for better accessibility */
*:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
  transition: ring-color 0.2s ease-in-out;
}

/* Custom scrollbar styling for WebKit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply rounded-full bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply rounded-full bg-gray-300 dark:bg-gray-700;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-600;
}

/* Command palette specific styles */
.cmdk-root {
  @apply w-full;
}

.cmdk-input {
  @apply w-full bg-transparent py-2 text-gray-800 focus:outline-none dark:text-gray-200;
}

.cmdk-list {
  @apply max-h-[300px] overflow-y-auto py-2;
}

.cmdk-group-heading {
  @apply px-3 py-2 text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400;
}

.cmdk-item {
  @apply flex cursor-pointer items-center justify-between rounded-md px-3 py-2;
  transition: all 0.15s ease;
}

.cmdk-item[data-selected='true'] {
  @apply bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300;
}

.cmdk-item:not([data-selected='true']) {
  @apply text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800;
}

.cmdk-empty {
  @apply py-6 text-center text-gray-500 dark:text-gray-400;
}

/* Timeline card interaction styles */
.timeline-element-container {
  position: relative;
  transition: z-index 0s 0.3s;
}

.timeline-element-container.z-10 {
  transition: z-index 0s 0s;
}

.timeline-card {
  transition: opacity 0.3s ease;
}

.timeline-card:focus {
  @apply outline-none;
}

.timeline-card:focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
}

.vertical-timeline-element-content {
  transition:
    background-color 0.3s ease,
    border-left 0.3s ease,
    box-shadow 0.3s ease;
}

.vertical-timeline-element-content-arrow {
  transition: border-right 0.3s ease;
}

/* Skills component styles */
.skill-item {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease,
    background-color 0.3s ease;
}

.skill-item:hover {
  transform: translateY(-2px);
}

.skill-item:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
}

.skill-category-button {
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    transform 0.2s ease;
}

.skill-category-button:hover {
  transform: translateY(-1px);
}

.skill-category-button:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
}

.skill-category-button.active {
  @apply bg-blue-600 text-white shadow-md;
}

/* Keyboard navigation visual indicators */
kbd {
  @apply rounded bg-gray-200 px-1.5 py-0.5 text-xs text-gray-600 dark:bg-gray-700 dark:text-gray-400;
  font-family:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

/* Animation utilities */
.transition-all-200 {
  transition: all 0.2s ease;
}

.transition-all-300 {
  transition: all 0.3s ease;
}

.transition-transform-200 {
  transition: transform 0.2s ease;
}

.transition-opacity-300 {
  transition: opacity 0.3s ease;
}

.transition-colors-200 {
  transition:
    color 0.2s ease,
    background-color 0.2s ease,
    border-color 0.2s ease;
}

/* Helper classes for focus states */
.focus-ring {
  @apply focus:outline-none focus-visible:shadow-lg focus-visible:ring-4 focus-visible:ring-blue-500/50 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-gray-900;
}

/* Enhanced timeline-specific focus states */
.timeline-icon-focus {
  @apply focus:outline-none focus-visible:shadow-xl focus-visible:ring-4 focus-visible:ring-primary-500/60 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-slate-900;
  transition:
    ring-color 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
}

.timeline-card-focus {
  @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-400/50 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-slate-900;
  transition:
    ring-color 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
}

/* Enhanced hover effects for timeline cards */
.timeline-card-hover {
  transition:
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    backdrop-filter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-card-hover:hover {
  transform: translateY(-2px) scale(1.01);
  @apply border-primary-300/40 shadow-xl dark:border-primary-600/40;
  backdrop-filter: blur(16px);
}

/* Respect reduced motion for timeline hover effects */
@media (prefers-reduced-motion: reduce) {
  .timeline-card-hover {
    transition: none !important;
  }

  .timeline-card-hover:hover {
    transform: none !important;
  }

  .timeline-icon-focus,
  .timeline-card-focus {
    transition: none !important;
  }
}

.hover-lift {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  @apply shadow-md;
}

/* Scrollbar for command palette */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-300 {
  scrollbar-color: #d1d5db transparent;
}

.dark .scrollbar-thumb-gray-700 {
  scrollbar-color: #374151 transparent;
}

/* Subtle animations for first-time visitors */
@keyframes subtle-pulse {

  0%,
  100% {
    opacity: 0.8;
  }

  50% {
    opacity: 1;
  }
}

/* Timeline icon pulse animation */
@keyframes timeline-pulse {

  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }

  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

/* New animation keyframes */
@keyframes sheen {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

@keyframes bounce-slow {

  0%,
  100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }

  50% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-subtle-pulse {
  animation: subtle-pulse 2s ease-in-out infinite;
}

.animate-gradient-shift {
  animation: sheen 2.5s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 3s infinite;
}

.animate-timeline-pulse {
  animation: timeline-pulse 2s infinite;
}

/* Screen reader only utility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Ensure proper stacking for interactive elements */
.z-command-palette {
  z-index: 50;
}

.z-timeline-expanded {
  z-index: 15;
}

/* Enhanced z-index for timeline elements to prevent overlap */
.z-timeline-card {
  z-index: 5;
}

.z-timeline-card-expanded {
  z-index: 15;
}

/* ◀︎ LLM-modified: Custom utility using design tokens */
.border-l-highlight {
  @apply border-l-4 border-blue-500;
  transition: border-left-color var(--duration-normal) var(--easing-smooth);
}

/* ◀︎ LLM-modified: Organic flowing layout helpers using design tokens */
.organic-position {
  position: absolute;
  transition: all 0.4s var(--easing-spring);
}

.skill-filter-active {
  @apply scale-105 shadow-md;
}

.skill-filter-inactive {
  @apply scale-90 opacity-40;
}

/* Icon button utilities */
.icon-btn-primary {
  background: linear-gradient(90deg,
      var(--gradient-from),
      var(--gradient-to),
      var(--gradient-to-dark));
  background-size: 200% 100%;
  color: white;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.icon-btn-primary:hover {
  animation: sheen 2.5s ease-in-out infinite;
  background-size: 200% auto;
  box-shadow: var(--token-glow-primary);
}

/* ◀︎ LLM-modified: Removed unused .command-button:hover class - components now use Framer Motion for hover effects */

/* Ensure proper text contrast in all states */
.text-high-contrast {
  @apply text-gray-900 dark:text-white;
}

.text-medium-contrast {
  @apply text-gray-700 dark:text-gray-300;
}

.text-low-contrast {
  @apply text-gray-500 dark:text-gray-400;
}

/* Spacing utility classes using CSS variables */
.margin-component-y {
  margin-top: var(--space-component) !important;
  margin-bottom: var(--space-component) !important;
}

/* Optimized CSS Transitions for Theme Changes */
/* Smooth theme transitions without View Transitions API to prevent white flash */

/* ◀︎ LLM-modified: Enhanced CSS transitions using design tokens */
html {
  transition: background-color var(--duration-hover) var(--easing-smooth);
}

body {
  transition:
    background-color var(--duration-hover) var(--easing-smooth),
    color var(--duration-hover) var(--easing-smooth);
}

/* Smooth transitions for theme-aware elements */
.App {
  transition:
    background-color var(--duration-hover) var(--easing-smooth),
    color var(--duration-hover) var(--easing-smooth);
}

/* Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {

  html,
  body,
  .App {
    transition-duration: 0.01ms !important;
  }
}

/* Timeline Flexbox Layout System - Simplified and Reliable */
.timeline-grid-container {
  /* Mobile: Single column layout */
  display: block;
  position: relative;
}

@media (min-width: 768px) {
  .timeline-grid-container {
    /* Desktop: Container for timeline with centered line */
    display: block;
    position: relative;
    max-width: 1152px;
    margin: 0 auto;
  }
}

/* Timeline Items - Flexbox Approach for Better Control */
.timeline-grid-item {
  /* Mobile: Standard flex layout */
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: relative;
  gap: 16px;
  /* 8px scale: 2 * 8 = 16px gap between icon and content */
}

@media (min-width: 768px) {
  .timeline-grid-item {
    /* Desktop: Flexbox layout for alternating timeline */
    display: flex;
    align-items: flex-start;
    position: relative;
    min-height: 96px;
    width: 100%;
    padding: 0 2rem;
    /* Add horizontal padding for spacing */
  }

  .timeline-grid-item.timeline-left {
    /* Left-side items: content on left, icon positioned absolutely at center */
    flex-direction: row;
    justify-content: flex-end;
    /* Push content to right side of left half */
    padding-right: calc(50% + 44px);
    /* Leave space for centered icon (44px = full icon width for safety) */
    padding-left: 2rem;
  }

  .timeline-grid-item.timeline-right {
    /* Right-side items: content on right, icon positioned absolutely at center */
    flex-direction: row;
    justify-content: flex-start;
    /* Push content to left side of right half */
    padding-left: calc(50% + 44px);
    /* Leave space for centered icon (44px = full icon width for safety) */
    padding-right: 2rem;
  }
}

/* Timeline Flexbox Positioning */
@media (min-width: 768px) {
  .timeline-card-wrapper {
    /* Cards positioned by flexbox */
    max-width: 400px;
    flex-shrink: 0;
    /* Prevent cards from shrinking */
  }

  .timeline-left .timeline-card-wrapper {
    /* Left cards: no additional margins needed - flexbox handles positioning */
    margin-right: 1rem;
    /* Reduced space since padding already creates separation */
    margin-left: 0;
  }

  .timeline-right .timeline-card-wrapper {
    /* Right cards: no additional margins needed - flexbox handles positioning */
    margin-left: 1rem;
    /* Reduced space since padding already creates separation */
    margin-right: 0;
  }

  /* Timeline icons positioned absolutely at center */
  .timeline-grid-item .timeline-icon {
    /* Position absolutely at the center of the timeline */
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }

  /* Icons are centered regardless of left/right side */
  .timeline-left .timeline-icon,
  .timeline-right .timeline-icon {
    /* All icons positioned at absolute center */
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
}

/* ===================================================================
     DESKTOP YEAR LABEL POSITIONING - FLEXBOX-BASED SOLUTION
     Year labels positioned above cards with proper alignment
     =================================================================== */

/* Desktop styles (md breakpoint and up) */
@media (min-width: 768px) {
  .timeline-grid-container .timeline-left .timeline-year-label-above {
    /* Left cards: center align year labels above cards */
    text-align: center;
  }

  .timeline-grid-container .timeline-right .timeline-year-label-above {
    /* Right cards: center align year labels above cards */
    text-align: center;
  }
}

/* ===================================================================
     CONTENT CARD GRID POSITIONING
     Ensure main content divs are positioned correctly within the grid
     =================================================================== */

/* Desktop: Card wrappers are positioned by grid template areas */
/* No explicit grid-column needed - handled by grid-area: content */

/* Timeline-specific layout utilities */
.timeline-card-container {
  position: relative;
  transition: margin-bottom 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-card-container.expanded {
  margin-bottom: 3rem;
}

/* Improved visual association utilities */
.timeline-visual-connection {
  position: relative;
}

/* Timeline section responsive container */
#timeline {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* View Transitions API Support */
/* Progressive enhancement for browsers that support View Transitions */

/* Timeline card view transition names - dynamically assigned via data attributes */
.timeline-card[data-view-transition-name] {
  view-transition-name: var(--view-transition-name);
}

.timeline-expanded-content[data-view-transition-name] {
  view-transition-name: var(--view-transition-name);
}

/* View Transition animations - only apply when motion is not reduced */
@media (prefers-reduced-motion: no-preference) {

  /* Smooth fade and scale transitions for timeline cards */
  ::view-transition-old(timeline-card-1),
  ::view-transition-old(timeline-card-2),
  ::view-transition-old(timeline-card-3),
  ::view-transition-old(timeline-card-4),
  ::view-transition-old(timeline-card-5),
  ::view-transition-old(timeline-card-6),
  ::view-transition-old(expanded-content-1),
  ::view-transition-old(expanded-content-2),
  ::view-transition-old(expanded-content-3),
  ::view-transition-old(expanded-content-4),
  ::view-transition-old(expanded-content-5),
  ::view-transition-old(expanded-content-6) {
    animation: view-transition-fade-out 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  ::view-transition-new(timeline-card-1),
  ::view-transition-new(timeline-card-2),
  ::view-transition-new(timeline-card-3),
  ::view-transition-new(timeline-card-4),
  ::view-transition-new(timeline-card-5),
  ::view-transition-new(timeline-card-6),
  ::view-transition-new(expanded-content-1),
  ::view-transition-new(expanded-content-2),
  ::view-transition-new(expanded-content-3),
  ::view-transition-new(expanded-content-4),
  ::view-transition-new(expanded-content-5),
  ::view-transition-new(expanded-content-6) {
    animation: view-transition-fade-in 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* View Transition keyframes */
@keyframes view-transition-fade-out {
  from {
    opacity: 1;
    transform: scale(1);
  }

  to {
    opacity: 0;
    transform: scale(0.98);
  }
}

@keyframes view-transition-fade-in {
  from {
    opacity: 0;
    transform: scale(0.98);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.timeline-visual-connection::before {
  content: '';
  position: absolute;
  top: 50%;
  width: 2rem;
  height: 2px;
  background: linear-gradient(90deg, var(--primary), transparent);
  transform: translateY(-50%);
  z-index: 5;
}

.timeline-visual-connection.left::before {
  right: 100%;
  background: linear-gradient(270deg, var(--primary), transparent);
}

.timeline-visual-connection.right::before {
  left: 100%;
  background: linear-gradient(90deg, var(--primary), transparent);
}

/* ===================================================================
   TIMELINE YEAR LABEL BASE STYLES - GRID-AWARE POSITIONING
   Removes absolute positioning to enable proper CSS Grid placement
   =================================================================== */

.timeline-year-label {
  /* --- FIX: Remove absolute positioning to enable Grid placement --- */
  position: static;
  /* Critical fix - allows grid to control placement */
  z-index: auto;
  /* Reset z-index */
  pointer-events: none;

  /* Mobile styles (default - applies below md breakpoint) */
  /* Position the year label in the second grid column (1fr) */
  /* This places it to the right of the icon (in column 1) on mobile */
  grid-column: 2 / 3;
  /* Align text to the left for mobile */
  text-align: left;
  /* Vertically align year label within its grid cell */
  align-self: center;
  /* Add left margin for spacing from the icon (16px = spacing-4 in 8px scale) */
  margin-left: 16px;
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
}

/* ===================================================================
   NEW: TIMELINE YEAR LABEL ABOVE CARDS - ABSOLUTE POSITIONING
   Positions year labels directly above their corresponding content cards
   =================================================================== */

.timeline-year-label-above {
  /* Position absolutely above the card */
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);

  /* Spacing from card */
  margin-bottom: 8px;

  /* Prevent text wrapping */
  white-space: nowrap;

  /* Ensure proper layering */
  z-index: 10;
  pointer-events: none;

  /* Center text alignment */
  text-align: center;

  /* Ensure year label doesn't take full width if not needed */
  width: auto;
  /* Ensure labels are readable and don't interfere with interactions */
  user-select: none;
  /* Improve text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile-specific timeline improvements - CSS Grid Compatible */
@media (max-width: 768px) {
  .timeline-card-container {
    margin-bottom: 1.5rem !important;
  }

  .timeline-card-container.expanded {
    margin-bottom: 2.5rem !important;
  }

  /* Mobile: Use CSS Grid layout for timeline items */
  .timeline-grid-item {
    display: grid !important;
    grid-template-columns: 44px 1fr !important;
    grid-template-rows: auto 1fr !important;
    gap: 8px 16px !important;
    align-items: start;
  }

  /* Ensure proper touch targets on mobile */
  .timeline-card {
    min-height: 44px;
    padding: 1rem !important;
  }

  /* Mobile timeline icons: grid-based positioning */
  .timeline-icon {
    /* Mobile: Icon in the first column, spanning both rows */
    grid-column: 1 / 2;
    grid-row: 1 / 3;
    justify-self: center;
    align-self: start;
    margin-top: 8px;
    /* Override desktop absolute positioning on mobile */
    position: static !important;
    left: auto !important;
    top: auto !important;
    transform: none !important;
    right: auto !important;
    bottom: auto !important;
  }

  /* Mobile year labels: positioned above card content */
  .timeline-year-label {
    grid-column: 2 / 3;
    grid-row: 1;
    text-align: left !important;
    margin-bottom: 8px;
    align-self: start;
  }

  /* Mobile: Year labels above cards - adjust positioning for mobile layout */
  .timeline-year-label-above {
    /* On mobile, position relative to maintain grid flow */
    position: relative;
    bottom: auto;
    left: auto;
    transform: none;
    margin-bottom: 8px;
    text-align: left;
    width: 100%;
  }

  /* Ensure timeline container doesn't overflow on mobile */
  #timeline .timeline-grid-container {
    overflow-x: visible;
    max-width: 100%;
    display: block !important;
  }

  /* Mobile timeline cards: full width with proper spacing and icon clearance */
  #timeline .timeline-card {
    width: 100%;
    max-width: none;
    /* Ensure content doesn't overlap with icon */
    margin-left: 0;
  }

  /* Mobile card wrapper: positioned in second column below year */
  .timeline-card-wrapper {
    grid-column: 2 / 3;
    grid-row: 2;
    width: 100% !important;
    padding-left: 0 !important;
  }
}

/* ===================================================================
   TABLET-SPECIFIC TIMELINE OPTIMIZATIONS
   Enhanced spacing and positioning for tablet viewports
   =================================================================== */

@media (min-width: 768px) and (max-width: 1023px) {

  /* Ensure cards maintain optimal distance from timeline on tablets */
  #timeline .timeline-card {
    max-width: 18rem;
  }

  /* Tablet timeline container: flexbox enhanced spacing */
  .timeline-grid-container {
    /* Flexbox properties already defined above */
    max-width: 1152px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  /* Tablet card wrapper: optimal width */
  .timeline-card-wrapper {
    max-width: 20rem;
  }

  /* Tablet year label positioning refinements - flexbox-based */
  .timeline-grid-container .timeline-left .timeline-year-label {
    /* Tablet: Right-align text and position */
    text-align: right;
    margin-right: 0;
    margin-left: 0;
    margin-bottom: 0;
  }

  .timeline-grid-container .timeline-right .timeline-year-label {
    /* Tablet: Left-align text and position */
    text-align: left;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 0;
  }

  /* Tablet: Timeline icon positioning uses CSS Grid (inherits from base rules above) */

  /* Tablet year labels: ensure proper visual hierarchy */
  #timeline .timeline-card p:first-child {
    margin-top: 0.125rem;
    /* 2px spacing for tablets */
  }
}

/* Desktop enhancements - CSS Grid Optimized */
@media (min-width: 768px) {
  .timeline-card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Desktop timeline container: CSS Grid enhanced spacing */
  .timeline-grid-container {
    /* Grid properties already defined above */
    max-width: 1152px;
    /* 6xl equivalent */
    margin-left: auto;
    margin-right: auto;
    padding: 0 2rem;
    /* Add horizontal padding for spacing */
  }

  /* Desktop timeline line: Absolutely positioned at center */
  .timeline-grid-container .timeline-line-desktop {
    /* Position absolutely at center of container */
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 46px;
    /* Start at first icon center */

    /* Ensure it's visible on desktop/tablet */
    display: block;
    width: 2px;
    /* Slightly thicker for better visibility */

    /* Layer behind icons but visible */
    z-index: 1;
  }

  /* Desktop timeline cards: CSS Grid responsive sizing */
  #timeline .timeline-card {
    width: auto;
    max-width: initial;
  }

  /* Desktop: Timeline icon positioning already handled by flexbox rules above */
  /* Icons are positioned absolutely at center using the flexbox approach */

  /* Desktop: Year labels above cards - enhanced positioning */
  .timeline-year-label-above {
    /* Increase spacing on desktop for better visual hierarchy */
    margin-bottom: 16px;

    /* Ensure proper positioning above cards */
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);

    /* Center text alignment for desktop */
    text-align: center;
    width: auto;
  }

  /* Desktop card wrapper: responsive max-widths */
  .timeline-card-wrapper {
    max-width: 24rem;
    /* Base max-width */
  }

  .timeline-card-wrapper.md\:max-w-md {
    max-width: 28rem;
  }

  .timeline-card-wrapper.lg\:max-w-lg {
    max-width: 32rem;
  }

  .timeline-card-wrapper.xl\:max-w-xl {
    max-width: 36rem;
  }

  /* Desktop year labels: minimal spacing since cards are positioned away from icons */
  #timeline .timeline-card p:first-child {
    margin-top: 0.125rem;
    /* 2px spacing for visual hierarchy */
  }
}

/* ===================================================================
   LARGE DESKTOP OPTIMIZATIONS
   Enhanced positioning and spacing for large screens
   =================================================================== */

@media (min-width: 1024px) {

  /* Large desktop optimizations - flexbox-based positioning */
  .timeline-grid-container .timeline-left .timeline-year-label {
    /* Desktop: Right-align text and position */
    text-align: right;
    margin-right: 0;
    margin-left: 0;
    margin-bottom: 0;
  }

  .timeline-grid-container .timeline-right .timeline-year-label {
    /* Desktop: Left-align text and position */
    text-align: left;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 0;
  }
}

/* ===================================================================
   ACCESSIBILITY AND INTERACTION IMPROVEMENTS
   Focus states and reduced motion support
   =================================================================== */

/* Focus and hover state improvements for parent timeline items */
.timeline-grid-item:focus-within .timeline-year-label,
.timeline-grid-item:hover .timeline-year-label {
  /* Subtle enhancement when parent item is focused/hovered */
  opacity: 1;
  /* Simple scale effect that works with grid positioning */
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

/* Focus and hover state improvements for year labels above cards */
.timeline-grid-item:focus-within .timeline-year-label-above,
.timeline-grid-item:hover .timeline-year-label-above {
  /* Subtle enhancement when parent item is focused/hovered */
  opacity: 1;
  /* Scale effect that works with absolute positioning */
  transform: translateX(-50%) scale(1.05);
  transition: transform 0.2s ease-in-out;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  .timeline-year-label,
  .timeline-year-label-above {
    /* Remove transforms and transitions for users who prefer reduced motion */
    transform: none !important;
    transition: none !important;
  }

  .timeline-grid-item:focus-within .timeline-year-label,
  .timeline-grid-item:hover .timeline-year-label,
  .timeline-grid-item:focus-within .timeline-year-label-above,
  .timeline-grid-item:hover .timeline-year-label-above {
    transform: none !important;
  }

  /* For year labels above cards, maintain centering but remove scale */
  .timeline-year-label-above {
    transform: translateX(-50%) !important;
  }

  .timeline-grid-item:focus-within .timeline-year-label-above,
  .timeline-grid-item:hover .timeline-year-label-above {
    transform: translateX(-50%) !important;
  }
}

/* ===================================================================
   COMPONENT STYLING - HERO TITLE AND SKILL BAR
   Enhanced contrast and visual hierarchy improvements
   =================================================================== */

/* Hero title styling with text shadow for improved readability */
.hero-title {
  text-shadow: 0 1px 1px rgba(15, 23, 42, 0.3);
}

/* Skill bar styling with enhanced dark mode appearance */
.skill-bar {
  /* Base styling for skill bars */
  transition: all 0.3s ease;
}

.dark .skill-bar {
  /* Dark mode enhancement with subtle inner border */
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* Timeline year label styling in dark mode */
.dark .timeline-year-text {
  color: var(--token-timeline-label);
  /* Timeline year labels in dark mode */
}

/* Timeline specific styles */
.timeline-line-desktop {
  background-color: var(--token-timeline-line);
  transition: background-color 0.3s ease;
}

.timeline-dot {
  background-color: var(--token-timeline-dot);
  border: 2px solid var(--token-timeline-dot-border);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}