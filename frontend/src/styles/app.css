/* ◀︎ LLM-modified: Replaced legacy CSS variables with semantic design tokens */
/* All color and styling values now reference the comprehensive token system */

.dark {
  /* ◀︎ LLM-modified: Legacy variables now reference semantic tokens for backward compatibility */
  --color-bg-primary: var(--token-app-bg-primary);
  --color-bg-secondary: var(--token-app-bg-secondary);
  --color-text-primary: var(--token-app-text-primary);
  --color-text-secondary: var(--token-app-text-secondary);
  --color-accent: var(--token-app-accent);
  --color-accent-hover: var(--token-app-accent-hover);
  --color-accent-light: var(--token-app-accent-light);
  --color-accent-dark: var(--token-app-accent-dark);
  --color-border: var(--token-app-border);
  --color-shadow: var(--token-app-shadow);
  --gradient-primary: var(--token-gradient-primary);
}

.App {
  position: relative;
  overflow-x: hidden;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  min-width: 320px;
  /* Ensure minimum width for very small screens */
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Loading state */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-color: var(--color-bg-primary);
  color: var(--color-accent);
  font-size: 1.25rem;
  font-weight: bold;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

/* Smooth transitions for all elements - optimized for 60fps performance */
* {
  transition:
    background-color 0.15s ease,
    color 0.15s ease,
    border-color 0.15s ease;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-bounce-slow,
  .animate-pulse-slow,
  .animate-spin-slow,
  .animate-wiggle {
    animation: none !important;
  }

  html {
    scroll-behavior: auto !important;
  }
}

/* Power button styling */
.power-button-logo {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.power-button-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 8px 20px -4px rgba(59, 130, 246, 0.5);
  transition: all 0.3s ease;
}

/* ◀︎ LLM-modified: Power button hover using design tokens */
.power-button-inner:hover {
  transform: scale(var(--token-scale-hover));
  box-shadow: var(--token-shadow-card-hover);
}

/* Command palette styling */
.cmdk-root {
  width: 100%;
  max-width: 100%;
  background: var(--token-bg-frosted);
  /* Frosted glass effect for overlays */
  border-radius: 16px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.dark .cmdk-root {
  background: rgba(30, 41, 59, 0.65);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Keyboard navigation styles */
:focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent);
}

/* Text styling enhancements */
.text-accent {
  color: var(--color-accent);
}

/* ◀︎ LLM-modified: Skills section styling using design tokens */
.skill-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--token-spacing-card-padding);
  background-color: var(--token-glass-skill-card);
  border-radius: 1rem;
  backdrop-filter: var(--token-backdrop-blur-md);
  -webkit-backdrop-filter: var(--token-backdrop-blur-md);
  box-shadow: var(--token-glass-skill-shadow);
  border: 1px solid var(--token-glass-skill-border);
  transition: all var(--duration-normal) var(--easing-smooth);
}

.dark .skill-card {
  /* Dark mode values are handled by CSS custom property inheritance */
}

/* ◀︎ LLM-modified: Skill icon styling using design tokens */
.skill-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: color-mix(in srgb, var(--token-app-accent) 10%, transparent);
  margin-bottom: var(--token-spacing-component-gap);
}

.dark .skill-icon {
  background-color: color-mix(in srgb, var(--token-app-accent) 20%, transparent);
}

/* ◀︎ LLM-modified: Skill dot styling using design tokens */
.skill-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--token-gradient-primary);
  box-shadow: var(--token-glow-primary-subtle);
}

.skill-name {
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  color: var(--color-text-primary);
  line-height: 1.4;
}

/* Timeline styling */
.timeline-container {
  position: relative;
  padding-left: 2rem;
}

.timeline-line {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 10px;
  width: 2px;
  background-color: var(--color-accent-light);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
}

.timeline-dot {
  position: absolute;
  left: -2rem;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--color-accent-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-dot-inner {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--color-accent);
}

.timeline-content {
  background-color: var(--color-bg-secondary);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 4px 6px var(--color-shadow);
  border: 1px solid var(--color-border);
}

/* Mobile optimizations */
@media (max-width: 640px) {
  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .skill-card {
    padding: 1.25rem 0.75rem;
  }

  .skill-icon {
    width: 40px;
    height: 40px;
  }

  .skill-name {
    font-size: 0.875rem;
  }
}