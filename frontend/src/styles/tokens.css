/* Design Token System - Centralized 8-token color palette with light/dark mode support */
/* Maintains WCAG AA contrast compliance (≥4.5:1) for all body text */

:root {
  /* === 8-TOKEN COLOR PALETTE === */

  /* Primary - Electric Blue (Brand Color) */
  --token-primary-50: #eff6ff;
  --token-primary-100: #dbeafe;
  --token-primary-200: #bfdbfe;
  --token-primary-300: #93c5fd;
  --token-primary-400: #60a5fa;
  --token-primary-500: #3b82f6;
  /* Main brand color */
  --token-primary-600: #2563eb;
  --token-primary-700: #1d4ed8;
  --token-primary-800: #1e40af;
  --token-primary-900: #1e3a8a;
  --token-primary-950: #172554;

  /* Secondary - Deep Navy */
  --token-secondary-50: #f8fafc;
  --token-secondary-100: #f1f5f9;
  --token-secondary-200: #e2e8f0;
  --token-secondary-300: #cbd5e1;
  --token-secondary-400: #94a3b8;
  --token-secondary-500: #64748b;
  --token-secondary-600: #475569;
  --token-secondary-700: #334155;
  --token-secondary-800: #1e293b;
  /* Navy light */
  --token-secondary-900: #0f172a;
  /* Navy default */
  --token-secondary-950: #020617;
  /* Navy dark */

  /* Accent - Sky Blue */
  --token-accent-50: #f0f9ff;
  --token-accent-100: #e0f2fe;
  --token-accent-200: #bae6fd;
  --token-accent-300: #7dd3fc;
  --token-accent-400: #38bdf8;
  --token-accent-500: #0ea5e9;
  /* Main accent */
  --token-accent-600: #0284c7;
  --token-accent-700: #0369a1;
  --token-accent-800: #075985;
  --token-accent-900: #0c4a6e;
  --token-accent-950: #082f49;

  /* Success - Emerald Green */
  --token-success-50: #ecfdf5;
  --token-success-100: #d1fae5;
  --token-success-200: #a7f3d0;
  --token-success-300: #6ee7b7;
  --token-success-400: #34d399;
  --token-success-500: #10b981;
  --token-success-600: #059669;
  --token-success-700: #047857;
  --token-success-800: #065f46;
  --token-success-900: #064e3b;
  --token-success-950: #022c22;

  /* Warning - Amber */
  --token-warning-50: #fffbeb;
  --token-warning-100: #fef3c7;
  --token-warning-200: #fde68a;
  --token-warning-300: #fcd34d;
  --token-warning-400: #fbbf24;
  --token-warning-500: #f59e0b;
  --token-warning-600: #d97706;
  --token-warning-700: #b45309;
  --token-warning-800: #92400e;
  --token-warning-900: #78350f;
  --token-warning-950: #451a03;

  /* Error - Red */
  --token-error-50: #fef2f2;
  --token-error-100: #fee2e2;
  --token-error-200: #fecaca;
  --token-error-300: #fca5a5;
  --token-error-400: #f87171;
  --token-error-500: #ef4444;
  --token-error-600: #dc2626;
  --token-error-700: #b91c1c;
  --token-error-800: #991b1b;
  --token-error-900: #7f1d1d;
  --token-error-950: #450a0a;

  /* Neutral - Gray Scale */
  --token-neutral-50: #f9fafb;
  --token-neutral-100: #f3f4f6;
  --token-neutral-200: #e5e7eb;
  --token-neutral-300: #d1d5db;
  --token-neutral-400: #9ca3af;
  --token-neutral-500: #6b7280;
  --token-neutral-600: #4b5563;
  --token-neutral-700: #374151;
  --token-neutral-800: #1f2937;
  --token-neutral-900: #111827;
  --token-neutral-950: #030712;

  /* Surface - Canvas/Background */
  --token-surface-50: #ffffff;
  --token-surface-100: #fafafa;
  /* Light canvas */
  --token-surface-200: #f5f5f5;
  --token-surface-300: #eeeeee;
  --token-surface-400: #e0e0e0;
  --token-surface-500: #bdbdbd;
  --token-surface-600: #757575;
  --token-surface-700: #424242;
  --token-surface-800: #212121;
  --token-surface-900: #0f172a;
  /* Dark canvas */
  --token-surface-950: #000000;

  /* === SEMANTIC COLOR TOKENS === */
  /* Light Mode Semantic Colors */
  --token-bg-primary: var(--token-surface-50);
  /* #ffffff */
  --token-bg-secondary: var(--token-surface-100);
  /* #fafafa */
  --token-bg-tertiary: var(--token-surface-200);
  /* #f5f5f5 */

  --token-bg-frosted: rgba(15, 23, 42, 0.08);
  --token-bg-frosted-light: rgba(15, 23, 42, 0.08);
  --token-bg-frosted-strong: rgba(255, 255, 255, 0.12);
  /* ◀︎ LLM-modified: Further reduced opacity for better transparency */

  /* === GLASS SURFACE ALIAS TOKENS === */
  /* ◀︎ LLM-modified: Enhanced glass surface tokens for consistent frosted glass effects */
  --glass-surface-card: var(--token-bg-frosted);
  /* inherits per theme */
  --glass-surface-card-b: rgba(255, 255, 255, 0.20);
  /* border alias */

  /* ◀︎ LLM-modified: Comprehensive glass surface tokens */
  --token-glass-card-light: rgba(255, 255, 255, 0.65);
  --token-glass-card-dark: rgba(30, 41, 59, 0.5);
  --token-glass-border-light: rgba(255, 255, 255, 0.2);
  --token-glass-border-dark: rgba(255, 255, 255, 0.08);
  --token-glass-shadow-light: 0 4px 16px rgba(0, 0, 0, 0.05);
  --token-glass-shadow-dark: 0 4px 16px rgba(0, 0, 0, 0.2);

  /* Backdrop blur intensity tokens */
  --token-backdrop-blur-sm: blur(4px);
  --token-backdrop-blur-md: blur(8px);
  --token-backdrop-blur-lg: blur(16px);

  /* Glass effect combinations */
  --token-glass-skill-card: var(--token-glass-card-light);
  --token-glass-skill-border: var(--token-glass-border-light);
  --token-glass-skill-shadow: var(--token-glass-shadow-light);

  /* Glow effect tokens for interactive elements */
  --token-glow-primary: 0 12px 32px -8px rgba(59, 130, 246, 0.6), 0 0 0 1px rgba(59, 130, 246, 0.2);
  --token-glow-primary-subtle: 0 8px 24px -6px rgba(59, 130, 246, 0.4);

  /* ◀︎ LLM-modified: Enhanced glow and shadow tokens for modern effects */
  --token-glow-hover: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
  --token-glow-focus: 0 0 0 2px rgba(59, 130, 246, 0.5);
  --token-shadow-card: 0 4px 6px rgba(0, 0, 0, 0.1);
  --token-shadow-card-hover: 0 8px 20px -4px rgba(59, 130, 246, 0.5);
  --token-shadow-floating: 0 8px 32px rgba(0, 0, 0, 0.1);
  --token-shadow-tooltip: 0 4px 16px rgba(0, 0, 0, 0.15);

  --token-hero-gradient-mid: #2f73e7;
  --token-hero-gradient-mid-light: #2F73E7;

  --token-text-primary: var(--token-secondary-900);
  /* #0f172a - WCAG AA: 15.8:1 */
  --token-text-secondary: var(--token-text-secondary-light);
  /* WCAG AA compliant - ◀︎ LLM-modified: Improved contrast from #334155 to #1e293b for better light mode readability */
  --token-text-secondary-light: #1e293b;


  --token-text-tertiary: var(--token-neutral-500);
  /* #6b7280 - WCAG AA: 4.6:1 */

  --token-border-primary: var(--token-neutral-200);
  /* #e5e7eb */
  --token-border-secondary: var(--token-neutral-300);
  /* #d1d5db */

  --token-shadow-primary: rgba(15, 23, 42, 0.08);
  --token-shadow-secondary: rgba(15, 23, 42, 0.12);

  /* Interactive States */
  --token-interactive-primary: var(--token-primary-500);
  /* #3b82f6 */
  --token-interactive-primary-hover: var(--token-primary-600);
  /* #2563eb */
  --token-interactive-primary-active: var(--token-primary-700);
  /* #1d4ed8 */

  --token-interactive-secondary: var(--token-neutral-100);
  /* #f3f4f6 */
  --token-interactive-secondary-hover: var(--token-neutral-200);
  /* #e5e7eb */
  --token-interactive-secondary-active: var(--token-neutral-300);
  /* #d1d5db */

  /* === HOVER SYSTEM TOKENS === */
  /* Mobile-first hover states with transform-based animations */

  /* Hover transforms - performance optimized */
  --hover-scale-sm: scale(1.02);
  --hover-scale-md: scale(1.05);
  --hover-scale-lg: scale(1.08);
  --hover-translate-y-sm: translateY(-2px);
  --hover-translate-y-md: translateY(-4px);
  --hover-translate-y-lg: translateY(-8px);

  /* === MOTION TOKENS === */
  /* Material 3 inspired timing system */
  --easing-standard: cubic-bezier(.4, 0, .2, 1);
  --motion-fast: 0.18s;
  --motion-medium: 0.25s;

  /* Hover timing functions */
  --hover-duration-fast: 150ms;
  --hover-duration-normal: 300ms;
  --hover-duration-slow: 500ms;
  --hover-ease: var(--easing-standard);
  --hover-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Hover background states */
  --hover-bg-primary: var(--token-interactive-primary-hover);
  --hover-bg-secondary: var(--token-interactive-secondary-hover);
  --hover-bg-tertiary: var(--token-neutral-50);
  --hover-bg-accent: var(--token-accent-100);

  /* Hover text states */
  --hover-text-primary: var(--token-text-primary);
  --hover-text-secondary: var(--token-primary-600);
  --hover-text-accent: var(--token-accent-600);

  /* Hover border states */
  --hover-border-primary: var(--token-primary-300);
  --hover-border-secondary: var(--token-neutral-300);
  --hover-border-accent: var(--token-accent-300);

  /* Hover shadow states */
  --hover-shadow-sm: 0 2px 4px -1px var(--token-shadow-primary);
  --hover-shadow-md: 0 4px 6px -1px var(--token-shadow-secondary), 0 2px 4px -1px var(--token-shadow-primary);
  --hover-shadow-lg: 0 10px 15px -3px var(--token-shadow-secondary), 0 4px 6px -2px var(--token-shadow-primary);
  --hover-shadow-glow: var(--token-glow-primary-subtle);

  /* === COMPREHENSIVE BORDER RADIUS TOKENS === */
  /* Consistent border radius system with semantic tokens */

  /* === BASE RADIUS TOKENS === */
  --token-radius-none: 0px;
  --token-radius-xs: 2px;
  --token-radius-sm: 4px;
  --token-radius-md: 8px;
  --token-radius-lg: 12px;
  --token-radius-xl: 16px;
  --token-radius-2xl: 20px;
  --token-radius-3xl: 24px;
  --token-radius-full: 9999px;

  /* === SEMANTIC RADIUS TOKENS === */
  /* Component-specific border radius values */
  --token-radius-button: var(--token-radius-md);
  /* 8px - standard button radius */
  --token-radius-card: var(--token-radius-lg);
  /* 12px - card container radius */
  --token-radius-input: var(--token-radius-md);
  /* 8px - form input radius */
  --token-radius-modal: var(--token-radius-xl);
  /* 16px - modal container radius */
  --token-radius-tooltip: var(--token-radius-sm);
  /* 4px - tooltip radius */
  --token-radius-badge: var(--token-radius-full);
  /* full - badge/pill radius */
  --token-radius-avatar: var(--token-radius-full);
  /* full - avatar radius */

  /* === RESPONSIVE RADIUS TOKENS === */
  /* Radius values that scale with viewport */
  --token-radius-responsive-sm: clamp(4px, 0.5vw, 6px);
  --token-radius-responsive-md: clamp(8px, 1vw, 12px);
  --token-radius-responsive-lg: clamp(12px, 1.5vw, 16px);

  /* Legacy aliases for backward compatibility */
  --radius-sm: var(--token-radius-sm);
  --radius-md: var(--token-radius-md);
  --radius-lg: var(--token-radius-lg);
  --radius-xl: var(--token-radius-xl);
  --radius-full: var(--token-radius-full);

  /* === SPACING TOKENS (8px scale) === */
  /* Base spacing scale - 8px increments */
  --space-0: 0px;
  --space-1: 8px;
  --space-2: 16px;
  --space-3: 24px;
  --space-4: 32px;
  --space-5: 40px;
  --space-6: 48px;
  --space-8: 64px;
  --space-10: 80px;
  --space-12: 96px;
  --space-16: 128px;
  --space-20: 160px;
  --space-24: 192px;
  --space-32: 256px;

  /* === RESPONSIVE SPACING TOKENS === */
  /* Component & Section Spacing - Responsive with clamp() */
  --token-spacing-component: clamp(16px, 3vw, 24px);
  /* Responsive component spacing: 16px mobile → 24px desktop */
  --token-spacing-section: clamp(40px, 6vw, 64px);
  /* Responsive section spacing: 40px mobile → 64px desktop */

  /* Legacy aliases for backward compatibility */
  --space-component: var(--token-spacing-component);
  --space-section: var(--token-spacing-section);

  /* Touch Target Spacing - WCAG 2.1 AA Compliance */
  --touch-target-min: 44px;
  /* Minimum touch target size */
  --touch-target-optimal: 48px;
  /* Optimal touch target size for better UX */
  --touch-clearance: var(--space-1);
  /* 8px thumb clearance around touch targets */

  /* === MOBILE-SPECIFIC SPACING TOKENS === */
  /* Optimized spacing for mobile viewports and thumb-friendly interactions */

  /* Mobile content spacing - reduced for screen efficiency */
  --mobile-space-xs: 4px;
  /* Extra small mobile spacing */
  --mobile-space-sm: 8px;
  /* Small mobile spacing */
  --mobile-space-md: 12px;
  /* Medium mobile spacing */
  --mobile-space-lg: 16px;
  /* Large mobile spacing */
  --mobile-space-xl: 20px;
  /* Extra large mobile spacing */

  /* Mobile touch zones - thumb-friendly interaction areas */
  --mobile-touch-zone-sm: 40px;
  /* Small touch zone */
  --mobile-touch-zone-md: 48px;
  /* Medium touch zone (optimal) */
  --mobile-touch-zone-lg: 56px;
  /* Large touch zone */

  /* Mobile safe areas - avoiding difficult reach zones */
  --mobile-safe-top: 16px;
  /* Safe distance from top edge */
  --mobile-safe-bottom: 24px;
  /* Safe distance from bottom edge */
  --mobile-safe-sides: 16px;
  /* Safe distance from side edges */



  /* === COMMAND MENU TOKENS === */
  /* ◀︎ LLM-modified: CommandMenu token system - updated for white text in light mode */

  /* CommandMenu text - light mode */
  --token-command-text: #0f172a;
  /* Dark text (text-secondary-900 equivalent) for better contrast in light mode */

  /* Section header text - light mode */
  --token-text-on-section: #ffffff;
  /* White text for section headers on dark section backgrounds */

  /* CommandMenu section labels - light mode */
  --token-command-label: #ffffff;
  /* White text for section labels in light mode for better visibility on frosted backgrounds */

  /* CommandMenu background - consistent across themes */
  --token-command-bg: var(--token-bg-frosted-strong);
  /* Frosted glass background */

  /* ◀︎ LLM-modified: Semantic tokens to replace legacy --color-* variables */
  /* App-level semantic tokens */
  --token-app-bg-primary: var(--token-bg-primary);
  --token-app-bg-secondary: var(--token-bg-secondary);
  --token-app-text-primary: var(--token-text-primary);
  --token-app-text-secondary: var(--token-text-secondary);
  --token-app-accent: var(--token-primary-500);
  --token-app-accent-hover: var(--token-primary-400);
  --token-app-accent-light: var(--token-primary-400);
  --token-app-accent-dark: var(--token-primary-700);
  --token-app-border: var(--token-text-secondary);
  --token-app-shadow: rgba(0, 0, 0, 0.25);

  /* Gradient tokens */
  --token-gradient-primary: linear-gradient(135deg, var(--token-primary-500), var(--token-primary-700));
  --token-gradient-button: var(--token-gradient-primary);
  --token-gradient-button-hover: linear-gradient(135deg, var(--token-primary-400), var(--token-primary-600));

  /* === CONSOLIDATED SPACING TOKENS === */
  /* ◀︎ LLM-modified: Consolidated spacing tokens - removed redundant tokens, kept only responsive versions following --token-category-property-variant naming convention */

  /* Legacy tokens - DEPRECATED: Use responsive versions below */
  /* --token-spacing-item-gap: REMOVED - use --token-spacing-responsive-item-gap */
  /* --token-command-item-gap: REMOVED - use --token-spacing-responsive-item-gap */

  /* CommandMenu container padding - legacy (non-responsive) */
  --token-command-padding-x: 1rem;
  /* 16px - horizontal padding for menu container */
  --token-command-padding-y: 0.75rem;
  /* 12px - vertical padding for menu container */

  /* CommandMenu item padding - legacy (non-responsive) */
  --token-command-item-padding-x: 0.75rem;
  /* 12px - horizontal padding for menu items */
  --token-command-item-padding-y: 0.5rem;
  /* 8px - vertical padding for menu items */

  /* CommandMenu group heading margin */
  --token-command-group-heading-margin: 0.5rem;
  /* 8px - margin below group headings */

  /* === FLOATING ACTION BUTTON POSITIONING === */
  /* ◀︎ LLM-modified: Responsive positioning tokens to prevent CTA overlap */

  /* Mobile positioning - prevent overlap with header content */
  --fab-mobile-bottom: var(--space-2);
  /* 16px */
  --fab-mobile-side: var(--space-2);
  /* 16px */

  /* Tablet positioning */
  --fab-tablet-bottom: var(--space-3);
  /* 24px */
  --fab-tablet-side: var(--space-3);
  /* 24px */

  /* Desktop positioning */
  --fab-desktop-bottom: var(--space-6);
  /* 48px */
  --fab-desktop-side: var(--space-6);
  /* 48px */

  /* Alignment tokens for perfect horizontal/vertical alignment */
  --fab-alignment-offset: 0px;
  /* Fine-tune alignment between buttons */
  --fab-center-offset: 50%;
  /* Center positioning for scroll button */

  /* Ring accent color for focus states */
  --ring-accent: var(--token-primary-500);
  /* Hover scale for floating action buttons */
  --fab-hover-scale: 1.05;

  /* === RESPONSIVE FLOATING BUTTON SIZING === */
  /* ◀︎ LLM-modified: Cleaned up unused tokens - components now use clamp() directly for better performance */

  /* Note: Floating action buttons now use clamp() functions directly in components:
   * Button size: clamp(2.75rem, 4vw, 3.5rem) - 44px mobile to 56px desktop
   * Icon size: clamp(1rem, 2.5vw, 1.5rem) - 16px mobile to 24px desktop
   * This approach provides better performance and maintainability
   */

  /* === RESPONSIVE COMMAND MENU TOKENS === */
  /* ◀︎ LLM-modified: Enhanced responsive tokens for CommandMenu component */

  /* CommandMenu container responsive sizing */
  --token-command-width-mobile: calc(100vw - 2rem);
  /* Full width minus 32px margin */
  --token-command-width-tablet: 28rem;
  /* 448px - comfortable tablet width */
  --token-command-width-desktop: 32rem;
  /* 512px - optimal desktop width */

  /* Responsive padding using clamp() for smooth scaling */
  --token-command-padding-responsive-x: clamp(1rem, 3vw, 1.5rem);
  /* 16px to 24px horizontal padding */
  --token-command-padding-responsive-y: clamp(0.75rem, 2vw, 1rem);
  /* 12px to 16px vertical padding */

  /* Responsive item padding */
  --token-command-item-padding-responsive-x: clamp(0.75rem, 2vw, 1rem);
  /* 12px to 16px item horizontal padding */
  --token-command-item-padding-responsive-y: clamp(0.5rem, 1.5vw, 0.75rem);
  /* 8px to 12px item vertical padding */

  /* Responsive typography scaling */
  --token-typography-size-responsive-md: clamp(0.875rem, 2.5vw, 1rem);
  /* 14px to 16px for headings */
  --token-typography-size-responsive-sm: clamp(0.8125rem, 2vw, 0.875rem);
  /* 13px to 14px for items */

  /* Responsive icon sizing */
  --token-size-icon-responsive: clamp(1.25rem, 3vw, 1.5rem);
  /* 20px to 24px responsive icon size */

  /* Responsive spacing */
  --token-spacing-responsive-item-gap: clamp(0.5rem, 1.5vw, 0.75rem);
  /* 8px to 12px responsive item gap */
  --token-spacing-responsive-group-gap: clamp(1rem, 2.5vw, 1.25rem);
  /* 16px to 20px responsive group gap */

  /* === TYPOGRAPHY SCALE TOKENS === */
  /* Semantic typography system for consistent visual hierarchy */

  /* Command menu typography */
  --text-command-title: 1.25rem;
  /* 20px - Modal/menu titles */
  --text-command-subtitle: 1rem;
  /* 16px - Section headers */
  --text-command-body: 0.875rem;
  /* 14px - Menu items */
  --text-command-caption: 0.75rem;
  /* 12px - Helper text */

  /* Font weights for hierarchy */
  --weight-command-title: 600;
  /* Semibold for titles */
  --weight-command-subtitle: 500;
  /* Medium for subtitles */
  --weight-command-body: 400;
  /* Normal for body text */
  --weight-command-caption: 400;
  /* Normal for captions */

  /* === COMPREHENSIVE TYPOGRAPHY TOKENS === */
  /* ◀︎ LLM-modified: Complete typography system with responsive design and semantic tokens */

  /* === BASE FONT SIZE TOKENS === */
  /* Fixed font sizes - 8px scale aligned */
  --token-typography-size-xs: 0.75rem;
  /* 12px */
  --token-typography-size-sm: 0.875rem;
  /* 14px */
  --token-typography-size-base: 1rem;
  /* 16px */
  --token-typography-size-lg: 1.125rem;
  /* 18px */
  --token-typography-size-xl: 1.25rem;
  /* 20px */
  --token-typography-size-2xl: 1.5rem;
  /* 24px */
  --token-typography-size-3xl: 1.875rem;
  /* 30px */
  --token-typography-size-4xl: 2.25rem;
  /* 36px */
  --token-typography-size-5xl: 3rem;
  /* 48px */
  --token-typography-size-6xl: 3.75rem;
  /* 60px */

  /* === RESPONSIVE TYPOGRAPHY TOKENS === */
  /* Fluid typography using clamp() for optimal scaling */
  --token-typography-size-responsive-xs: clamp(0.75rem, 1vw, 0.875rem);
  /* 12px → 14px */
  --token-typography-size-responsive-sm: clamp(0.875rem, 1.2vw, 1rem);
  /* 14px → 16px */
  --token-typography-size-responsive-md: clamp(1rem, 1.5vw, 1.125rem);
  /* 16px → 18px */
  --token-typography-size-responsive-lg: clamp(1.125rem, 2vw, 1.25rem);
  /* 18px → 20px */
  --token-typography-size-responsive-xl: clamp(1.25rem, 2.5vw, 1.5rem);
  /* 20px → 24px */
  --token-typography-size-responsive-2xl: clamp(1.5rem, 3vw, 1.875rem);
  /* 24px → 30px */
  --token-typography-size-responsive-3xl: clamp(1.875rem, 4vw, 2.25rem);
  /* 30px → 36px */
  --token-typography-size-responsive-4xl: clamp(2.25rem, 5vw, 3rem);
  /* 36px → 48px */
  --token-typography-size-responsive-5xl: clamp(3rem, 6vw, 3.75rem);
  /* 48px → 60px */

  /* === FONT WEIGHT TOKENS === */
  --token-typography-weight-light: 300;
  --token-typography-weight-regular: 400;
  --token-typography-weight-medium: 500;
  --token-typography-weight-semibold: 600;
  --token-typography-weight-bold: 700;
  --token-typography-weight-extrabold: 800;

  /* === LINE HEIGHT TOKENS === */
  --token-typography-leading-tight: 1.25;
  --token-typography-leading-snug: 1.375;
  --token-typography-leading-normal: 1.5;
  --token-typography-leading-relaxed: 1.625;
  --token-typography-leading-loose: 2;

  /* === SEMANTIC TYPOGRAPHY TOKENS === */
  /* Component-specific typography combinations */
  --token-typography-heading-hero: var(--token-typography-size-responsive-5xl);
  --token-typography-heading-primary: var(--token-typography-size-responsive-4xl);
  --token-typography-heading-secondary: var(--token-typography-size-responsive-3xl);
  --token-typography-heading-tertiary: var(--token-typography-size-responsive-2xl);
  --token-typography-heading-quaternary: var(--token-typography-size-responsive-xl);
  --token-typography-body-large: var(--token-typography-size-responsive-lg);
  --token-typography-body-medium: var(--token-typography-size-responsive-md);
  --token-typography-body-small: var(--token-typography-size-responsive-sm);
  --token-typography-caption: var(--token-typography-size-responsive-xs);

  /* === COMMAND MENU SPECIFIC TYPOGRAPHY === */
  --token-typography-command-heading: var(--token-typography-size-responsive-md);
  --token-typography-command-item: var(--token-typography-size-responsive-sm);
  --token-typography-command-caption: var(--token-typography-size-responsive-xs);

  /* Legacy aliases for backward compatibility */
  --leading-command-title: var(--token-typography-leading-snug);
  --leading-command-body: var(--token-typography-leading-normal);
  --leading-command-caption: var(--token-typography-leading-tight);

  /* === ICON SIZING TOKENS === */
  /* ◀︎ LLM-modified: Standardized icon sizing tokens following --token-category-property-variant naming convention */

  /* Icon sizes for consistent visual hierarchy */
  --token-size-icon: 1.5rem;
  /* 24px - standardized icon size for command menu items */
  --token-size-icon-container: 2.5rem;
  /* 40px - circular background container for icons */

  /* === BORDER TOKENS === */
  /* ◀︎ LLM-modified: Border tokens for dividers and subtle separations */

  /* Border widths */
  --token-border-width-divider: 1px;
  /* standard divider thickness */

  /* Border colors - light mode */
  --token-color-border-subtle: var(--token-neutral-200);
  /* subtle border color for dividers in light mode */

  /* CommandMenu divider styling */
  --token-command-divider-bg: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
  /* Glassmorphism divider for light mode */
  --token-command-divider-height: 1px;
  /* Standard divider height */

  /* === ACCESSIBILITY TOKENS === */
  /* ◀︎ LLM-modified: Enhanced accessibility and interaction states with comprehensive focus tokens */

  /* Global focus ring tokens */
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
  --focus-ring-color: var(--token-primary-500);

  /* CommandMenu-specific focus state tokens */
  --token-command-focus-ring-width: 2px;
  /* Standard focus ring width for command menu items */
  --token-command-focus-ring-offset: 2px;
  /* Focus ring offset for proper spacing */
  --token-command-focus-ring-color: var(--token-primary-500);
  /* Primary blue for focus indicators - WCAG AA compliant */
  --token-command-focus-shadow: 0 0 0 var(--token-command-focus-ring-width) var(--token-command-focus-ring-color);
  /* Complete focus shadow for enhanced visibility */

  /* Touch feedback tokens */
  --touch-feedback-scale: 0.97;
  /* Scale down on touch */
  --touch-feedback-duration: 100ms;
  /* Quick feedback */

  /* === SCROLLBAR TOKENS === */
  /* Custom scrollbar styling for consistent UX */

  /* Scrollbar dimensions */
  --scrollbar-width: 6px;
  --scrollbar-track-radius: 3px;
  --scrollbar-thumb-radius: 3px;

  /* === SHADOW TOKENS === */
  /* Consistent shadow system for elevation hierarchy */

  /* Base shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Primary colored shadows for interactive elements */
  --shadow-primary-sm: 0 8px 20px -4px rgba(59, 130, 246, 0.4);
  --shadow-primary-md: 0 12px 28px -4px rgba(59, 130, 246, 0.5);
  --shadow-primary-lg: 0 12px 32px -8px rgba(59, 130, 246, 0.6), 0 0 0 1px rgba(59, 130, 246, 0.2);

  /* Inset shadows */
  --shadow-inset: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  --shadow-inset-md: inset 0 -1px 0 rgba(0, 0, 0, 0.1);

  /* === HEIGHT TOKENS === */
  /* Consistent height system for components */

  /* Touch targets and interactive elements */
  --height-touch-min: 44px;
  /* WCAG AA minimum touch target */
  --height-touch-comfortable: 48px;
  /* Comfortable touch target */
  --height-button-sm: 32px;
  --height-button-md: 40px;
  --height-button-lg: 48px;
  --height-button-xl: 56px;

  /* Component heights */
  --height-header: 64px;
  --height-nav: 56px;
  --height-command-menu: 32rem;
  /* 512px */
  --height-command-list: 300px;

  /* ◀︎ LLM-modified: Enhanced responsive spacing tokens */
  /* Responsive spacing using clamp for smooth scaling */
  --token-spacing-header-x: clamp(1rem, 4vw, 1.5rem);
  --token-spacing-header-y: clamp(1rem, 4vw, 1.5rem);
  --token-spacing-card-padding: clamp(1rem, 3vw, 1.5rem);
  --token-spacing-section-y: clamp(2rem, 6vw, 4rem);
  --token-spacing-component-gap: clamp(1rem, 3vw, 2rem);

  /* Typography responsive tokens */
  --token-font-size-hero: clamp(2rem, 5vw, 3.5rem);
  --token-font-size-timeline-year: clamp(1.125rem, 2.5vw, 1.25rem);
  --token-line-height-hero: 1.1;
  --token-line-height-body: 1.6;
  --token-line-height-tight: 1.25;

  /* === BREAKPOINT TOKENS === */
  /* Responsive design breakpoints */

  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* === ANIMATION TIMING TOKENS === */
  /* Extended timing system for consistent animations */

  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 750ms;

  /* Specific animation durations */
  --duration-hover: 180ms;
  --duration-focus: 200ms;
  --duration-modal: 250ms;
  --duration-page-transition: 400ms;

  /* ◀︎ LLM-modified: Enhanced animation timing tokens for modern interactions */
  /* Component-specific animation durations */
  --duration-pulse: 3s;
  --duration-bounce: 2s;
  --duration-shimmer: 1.5s;
  --duration-fade-in: 1s;
  --duration-slide-up: 1s;
  --duration-tooltip: 200ms;

  /* ◀︎ LLM-modified: Morphing animation tokens for CommandMenu */
  --duration-morph-enter: 250ms;
  --duration-morph-exit: 200ms;
  --duration-layout-shift: 300ms;

  /* Animation easing functions */
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
  --easing-ease-out: ease-out;
  --easing-ease-in: ease-in;
  --easing-ease-in-out: ease-in-out;

  /* Scale transform tokens */
  --token-scale-hover: 1.05;
  --token-scale-tap: 0.95;
  --token-scale-focus: 1.02;
  --token-scale-pulse-min: 1;
  --token-scale-pulse-max: 1.02;

  /* Scrollbar colors - will be overridden in dark mode */
  --scrollbar-track-color: var(--token-primary-100);
  --scrollbar-thumb-color: var(--token-primary-300);
  --scrollbar-thumb-hover-color: var(--token-primary-400);

  /* === SECTION BACKGROUND ALIASES === */
  /* ◀︎ LLM-modified: Added section background aliases for consistent section styling */
  --section-primary-bg: var(--token-surface-50);
  --section-secondary-bg: var(--token-surface-100);

  /* === TIMELINE SPECIFIC TOKENS === */
  /* ◀︎ LLM-modified: Added timeline-specific tokens for consistent styling */
  --token-timeline-line: var(--token-primary-400);
  --token-timeline-dot: var(--token-primary-500);
  --token-timeline-dot-border: var(--token-primary-300);
}

/* === DARK MODE OVERRIDES === */
.dark {
  /* Dark Mode Semantic Colors */
  --token-bg-primary: var(--token-surface-900);
  /* #0f172a */
  --token-bg-secondary: var(--token-secondary-800);
  /* #1e293b */
  --token-bg-tertiary: var(--token-secondary-700);
  /* #334155 */

  --token-text-primary: #ffffff;
  /* Pure white for maximum contrast */
  --token-text-secondary: var(--token-text-secondary-dark);
  /* WCAG AA compliant */
  --token-text-secondary-dark: #E2E8F0;

  /* ◀︎ LLM-modified: CommandMenu colors for dark mode - only color overrides, spacing tokens remain the same */
  --token-command-text: #ffffff;
  /* Light text for visibility on dark backgrounds */

  /* ◀︎ LLM-modified: Dark mode semantic token overrides */
  --token-app-bg-primary: var(--token-surface-900);
  --token-app-bg-secondary: var(--token-secondary-800);
  --token-app-text-primary: #ffffff;
  --token-app-text-secondary: var(--token-text-secondary-dark);
  --token-app-accent: var(--token-primary-400);
  --token-app-accent-hover: var(--token-primary-300);
  --token-app-accent-light: var(--token-primary-300);
  --token-app-accent-dark: var(--token-primary-600);
  --token-app-shadow: rgba(0, 0, 0, 0.4);

  /* Section header text - dark mode */
  --token-text-on-section: #E2E8F0;
  /* Light gray text for section headers on dark section backgrounds */
  --token-command-label: #cbd5e1;
  /* Enhanced lighter gray for better visual hierarchy and contrast in dark mode */
  /* Note: --token-command-bg and all spacing tokens (padding, gaps, etc.) remain unchanged from light mode */
  --token-text-tertiary: var(--token-neutral-400);
  /* #9ca3af - WCAG AA: 4.6:1 */

  --token-timeline-label: #60A5FA;

  --token-bg-frosted: rgba(30, 41, 59, 0.65);
  /* ◀︎ LLM-modified: Dark mode frosted background */
  --token-bg-frosted-strong: rgba(255, 255, 255, 0.08);
  /* ◀︎ LLM-modified: Further reduced opacity for better transparency in dark mode */

  /* === GLASS SURFACE ALIAS TOKENS - DARK MODE === */
  /* ◀︎ LLM-modified: Dark mode overrides for glass surface aliases */
  --glass-surface-card: var(--token-bg-frosted);
  /* inherits dark mode frosted bg */
  --glass-surface-card-b: rgba(255, 255, 255, 0.15);
  /* reduced border opacity for dark mode */

  /* ◀︎ LLM-modified: Dark mode glass surface overrides */
  --token-glass-skill-card: var(--token-glass-card-dark);
  --token-glass-skill-border: var(--token-glass-border-dark);
  --token-glass-skill-shadow: var(--token-glass-shadow-dark);

  /* Backdrop blur intensity tokens - same for dark mode */
  --token-backdrop-blur-sm: blur(4px);
  --token-backdrop-blur-md: blur(8px);
  --token-backdrop-blur-lg: blur(16px);

  /* Glow effect tokens for dark mode - enhanced visibility */
  --token-glow-primary: 0 12px 32px -8px rgba(96, 165, 250, 0.8), 0 0 0 1px rgba(96, 165, 250, 0.3);
  --token-glow-primary-subtle: 0 8px 24px -6px rgba(96, 165, 250, 0.5);
  --token-glow-hover: 0 10px 25px -5px rgba(96, 165, 250, 0.5);
  --token-glow-focus: 0 0 0 2px rgba(96, 165, 250, 0.6);
  --token-shadow-card: 0 4px 6px rgba(0, 0, 0, 0.25);
  --token-shadow-card-hover: 0 8px 20px -4px rgba(96, 165, 250, 0.6);
  --token-shadow-floating: 0 8px 32px rgba(0, 0, 0, 0.3);
  --token-shadow-tooltip: 0 4px 16px rgba(0, 0, 0, 0.4);

  --token-border-primary: var(--token-secondary-700);
  /* #334155 */
  --token-border-secondary: var(--token-secondary-600);
  /* #475569 */

  /* Border colors - dark mode */
  --token-color-border-subtle: var(--token-neutral-700);
  /* subtle border color for dividers in dark mode */

  /* CommandMenu divider styling - dark mode */
  --token-command-divider-bg: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  /* Glassmorphism divider for dark mode - reduced opacity */

  --token-shadow-primary: rgba(0, 0, 0, 0.25);
  --token-shadow-secondary: rgba(0, 0, 0, 0.35);

  /* Dark mode scrollbar colors */
  --scrollbar-track-color: var(--token-secondary-800);
  --scrollbar-thumb-color: var(--token-secondary-600);
  --scrollbar-thumb-hover-color: var(--token-secondary-500);

  /* Dark mode focus ring */
  --focus-ring-color: var(--token-primary-400);

  /* CommandMenu focus tokens - dark mode overrides */
  --token-command-focus-ring-color: var(--token-primary-400);
  /* Lighter blue for better contrast on dark backgrounds - WCAG AA compliant */
  --token-command-focus-shadow: 0 0 0 var(--token-command-focus-ring-width) var(--token-command-focus-ring-color);
  /* Updated focus shadow for dark mode */

  /* Interactive States - Dark Mode */
  --token-interactive-primary: var(--token-primary-400);
  /* #60a5fa */
  --token-interactive-primary-hover: var(--token-primary-300);
  /* #93c5fd */
  --token-interactive-primary-active: var(--token-primary-200);
  /* #bfdbfe */

  --token-interactive-secondary: var(--token-secondary-700);
  /* #334155 */
  --token-interactive-secondary-hover: var(--token-secondary-600);
  /* #475569 */
  --token-interactive-secondary-active: var(--token-secondary-500);
  /* #64748b */

  /* === HOVER SYSTEM TOKENS - DARK MODE === */
  /* Dark mode hover states with enhanced visibility */

  /* Hover background states - dark mode */
  --hover-bg-primary: var(--token-interactive-primary-hover);
  --hover-bg-secondary: var(--token-interactive-secondary-hover);
  --hover-bg-tertiary: var(--token-secondary-600);
  --hover-bg-accent: var(--token-accent-800);

  /* Hover text states - dark mode */
  --hover-text-primary: #ffffff;
  --hover-text-secondary: var(--token-primary-300);
  --hover-text-accent: var(--token-accent-300);

  /* Hover border states - dark mode */
  --hover-border-primary: var(--token-primary-400);
  --hover-border-secondary: var(--token-secondary-500);
  --hover-border-accent: var(--token-accent-400);

  /* Hover shadow states - dark mode enhanced */
  --hover-shadow-sm: 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --hover-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --hover-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --hover-shadow-glow: var(--token-glow-primary-subtle);



  /* === SHADOW TOKENS - DARK MODE === */
  /* Enhanced shadows for dark mode with better visibility */

  /* Base shadows - enhanced for dark backgrounds */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 8px 10px -6px rgba(0, 0, 0, 0.6);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);

  /* Primary colored shadows for dark mode - enhanced visibility */
  --shadow-primary-sm: 0 8px 20px -4px rgba(96, 165, 250, 0.5);
  --shadow-primary-md: 0 12px 28px -4px rgba(96, 165, 250, 0.6);
  --shadow-primary-lg: 0 12px 32px -8px rgba(96, 165, 250, 0.8), 0 0 0 1px rgba(96, 165, 250, 0.3);

  /* Inset shadows for dark mode */
  --shadow-inset: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);
  --shadow-inset-md: inset 0 -1px 0 rgba(255, 255, 255, 0.1);

  /* === SECTION BACKGROUND ALIASES - DARK MODE === */
  /* ◀︎ LLM-modified: Dark mode overrides for section background aliases using color-mix() */
  --section-primary-bg: color-mix(in srgb, var(--token-primary-500) 62%, black 38%);
  --section-secondary-bg: color-mix(in srgb, var(--token-primary-800) 55%, black 45%);

  /* Timeline-specific tokens - dark mode */
  --token-timeline-line: var(--token-primary-500);
  --token-timeline-dot: var(--token-primary-400);
  --token-timeline-dot-border: var(--token-primary-600);
}

/* === GRADIENT TOKENS === */
/* Preserves existing gradient color schemes for backgrounds and components */
:root {
  /* Primary gradients - Electric Blue theme */
  --gradient-primary: linear-gradient(135deg, var(--token-primary-500), var(--token-primary-600));
  /* #3b82f6 to #2563eb */
  --gradient-primary-light: linear-gradient(135deg, var(--token-primary-400), var(--token-primary-500));
  /* #60a5fa to #3b82f6 */

  /* Secondary gradients - Navy to Blue theme */
  --gradient-secondary: linear-gradient(135deg, var(--token-secondary-800), var(--token-primary-500));
  /* #1e293b to #3b82f6 */
  --gradient-secondary-dark: linear-gradient(135deg, var(--token-secondary-900), var(--token-primary-600));
  /* #0f172a to #2563eb */

  /* Header background gradient - Slate to Blue */
  --gradient-header: linear-gradient(135deg, #4f7cdb 0%, var(--token-hero-gradient-mid) 35%, #3b82f6 100%);
  /* ◀︎ LLM-modified: Replaced dark corner #14316a with lighter #4f7cdb for better light mode visibility while maintaining brand consistency */

  /* Button gradients - Blue theme */
  --gradient-button: linear-gradient(to right, var(--token-primary-500), var(--token-primary-600));
  /* blue-500 to blue-600 */
  --gradient-button-hover: linear-gradient(to right, var(--token-primary-600), var(--token-primary-700));
  /* blue-600 to blue-700 */

  /* Enhanced command button styling for better discoverability */
  --token-command-button-bg: var(--token-primary-500);
  /* Solid background for visibility on all backgrounds - LLM-modified */
  --token-command-button-border: var(--token-primary-600);
  /* Solid border for better definition - LLM-modified */

  /* Accent gradients */
  --gradient-accent: linear-gradient(135deg, var(--token-accent-500), var(--token-accent-600));

  /* Timeline gradients */
  --gradient-timeline: linear-gradient(to bottom, var(--token-primary-400), var(--token-primary-500), var(--token-primary-600));
}

.dark {
  /* Dark mode gradient overrides - preserves existing dark theme */
  --gradient-primary: linear-gradient(135deg, var(--token-primary-500), var(--token-primary-700));
  /* #3b82f6 to #1d4ed8 */
  --gradient-primary-light: linear-gradient(135deg, var(--token-primary-300), var(--token-primary-400));
  /* #93c5fd to #60a5fa */

  /* Secondary gradients for dark mode */
  --gradient-secondary: linear-gradient(135deg, var(--token-secondary-800), var(--token-primary-400));
  /* #1e293b to #60a5fa */
  --gradient-secondary-dark: linear-gradient(135deg, var(--token-secondary-900), var(--token-primary-500));
  /* #0f172a to #3b82f6 */

  /* Header background gradient - Enhanced for dark mode */
  --gradient-header: linear-gradient(to bottom right, var(--token-secondary-900), var(--token-primary-500));
  /* slate-900 to blue-500 */

  /* Button gradients - Adjusted for dark mode visibility */
  --gradient-button: linear-gradient(to right, var(--token-primary-400), var(--token-primary-500));
  /* blue-400 to blue-500 */
  --gradient-button-hover: linear-gradient(to right, var(--token-primary-300), var(--token-primary-400));
  /* blue-300 to blue-400 */

  /* Enhanced command button styling for dark mode */
  --token-command-button-bg: var(--token-primary-500);
  /* Solid background for visibility on all backgrounds - LLM-modified */
  --token-command-button-border: var(--token-primary-600);
  /* Solid border for better definition - LLM-modified */

  /* Accent gradients for dark mode */
  --gradient-accent: linear-gradient(135deg, var(--token-accent-400), var(--token-accent-500));

  /* Timeline gradients for dark mode */
  --gradient-timeline: linear-gradient(to bottom, var(--token-primary-300), var(--token-primary-400), var(--token-primary-500));
}

/* ◀︎ LLM-modified: CSS Debugging utilities for hero gradient troubleshooting */
/* Debug class to test gradient application - remove after debugging */
.debug-gradient-header {
  background: var(--gradient-header) !important;
  border: 2px solid red !important;
}

/* Debug class to test if custom properties are working */
.debug-hero-gradient-mid {
  background: var(--token-hero-gradient-mid) !important;
  color: white !important;
  padding: 10px !important;
}

/* === ANIMATION KEYFRAMES === */
/* ◀︎ LLM-modified: Added missing keyframe animations for interactive elements */

@keyframes sheen {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes timeline-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

@keyframes bounce-slow {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}