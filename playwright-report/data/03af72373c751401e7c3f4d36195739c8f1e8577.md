# Test info

- Name: Accessibility Tests >> should not have any accessibility violations
- Location: /Users/<USER>/Developer/app/tests/a11y.spec.ts:43:7

# Error details

```
Error: expect(received).toHaveLength(expected)

Expected length: 0
Received length: 2
Received array:  [{"description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds", "help": "Elements must meet minimum color contrast ratio thresholds", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/color-contrast?application=playwright", "id": "color-contrast", "impact": "serious", "nodes": [{"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 3.18 (foreground color: #3b82f6, background color: #e7effe, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-xs px-2 py-1 rounded-full font-medium\" style=\"background-color: rgba(59, 130, 246, 0.125); color: rgb(59, 130, 246);\">Expert</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-1\"] > .p-5 > .mb-3.items-start.justify-between > .px-2.py-1.text-xs"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 13.5pt (18px), font weight: bold). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-lg font-bold\" style=\"color: rgb(59, 130, 246);\">95%</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-1\"] > .p-5 > .relative.mt-4 > .space-x-3.mb-1.items-center > .font-bold.text-lg"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 3.18 (foreground color: #3b82f6, background color: #e7effe, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-xs px-2 py-1 rounded-full font-medium\" style=\"background-color: rgba(59, 130, 246, 0.125); color: rgb(59, 130, 246);\">Expert</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-2\"] > .p-5 > .mb-3.items-start.justify-between > .px-2.py-1.text-xs"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 13.5pt (18px), font weight: bold). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-lg font-bold\" style=\"color: rgb(59, 130, 246);\">92%</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-2\"] > .p-5 > .relative.mt-4 > .space-x-3.mb-1.items-center > .font-bold.text-lg"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 3.18 (foreground color: #3b82f6, background color: #e7effe, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-xs px-2 py-1 rounded-full font-medium\" style=\"background-color: rgba(59, 130, 246, 0.125); color: rgb(59, 130, 246);\">Expert</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-3\"] > .p-5 > .mb-3.items-start.justify-between > .px-2.py-1.text-xs"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 13.5pt (18px), font weight: bold). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-lg font-bold\" style=\"color: rgb(59, 130, 246);\">90%</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-3\"] > .p-5 > .relative.mt-4 > .space-x-3.mb-1.items-center > .font-bold.text-lg"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 2.25 (foreground color: #10b981, background color: #e1f6ef, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-xs px-2 py-1 rounded-full font-medium\" style=\"background-color: rgba(16, 185, 129, 0.125); color: rgb(16, 185, 129);\">Advanced</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-4\"] > .p-5 > .mb-3.items-start.justify-between > .px-2.py-1.text-xs"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 2.53 (foreground color: #10b981, background color: #ffffff, font size: 13.5pt (18px), font weight: bold). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-lg font-bold\" style=\"color: rgb(16, 185, 129);\">88%</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-4\"] > .p-5 > .relative.mt-4 > .space-x-3.mb-1.items-center > .font-bold.text-lg"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 3.18 (foreground color: #3b82f6, background color: #e7effe, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-xs px-2 py-1 rounded-full font-medium\" style=\"background-color: rgba(59, 130, 246, 0.125); color: rgb(59, 130, 246);\">Expert</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-5\"] > .p-5 > .mb-3.items-start.justify-between > .px-2.py-1.text-xs"]}, {"all": [], "any": [[Object]], "failureSummary": "Fix any of the following:
  Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 13.5pt (18px), font weight: bold). Expected contrast ratio of 4.5:1", "html": "<span class=\"text-lg font-bold\" style=\"color: rgb(59, 130, 246);\">94%</span>", "impact": "serious", "none": [], "target": ["div[data-skill-id=\"skill-5\"] > .p-5 > .relative.mt-4 > .space-x-3.mb-1.items-center > .font-bold.text-lg"]}, …], "tags": ["cat.color", "wcag2aa", "wcag143", "TTv5", "TT13.c", "EN-301-549", "EN-*******", "ACT"]}, {"description": "Ensure touch targets have sufficient size and space", "help": "All touch targets must be 24px large, or leave sufficient space", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/target-size?application=playwright", "id": "target-size", "impact": "serious", "nodes": [{"all": [], "any": [[Object], [Object]], "failureSummary": "Fix any of the following:
  Target has insufficient size (20px by 20px, should be at least 24px by 24px)
  Target has insufficient space to its closest neighbors. Safe clickable space has a diameter of 16px instead of at least 24px.", "html": "<a href=\"https://github.com/nathan-dryer\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"social-icon-modern\" aria-label=\"GitHub\" tabindex=\"0\">", "impact": "serious", "none": [], "target": ["a[aria-label=\"GitHub\"]"]}, {"all": [], "any": [[Object], [Object]], "failureSummary": "Fix any of the following:
  Target has insufficient size (20px by 20px, should be at least 24px by 24px)
  Target has insufficient space to its closest neighbors. Safe clickable space has a diameter of 16px instead of at least 24px.", "html": "<a href=\"https://www.linkedin.com/in/nathandryer\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"social-icon-modern\" aria-label=\"LinkedIn\" tabindex=\"0\">", "impact": "serious", "none": [], "target": ["a[aria-label=\"LinkedIn\"]"]}, {"all": [], "any": [[Object], [Object]], "failureSummary": "Fix any of the following:
  Target has insufficient size (20px by 20px, should be at least 24px by 24px)
  Target has insufficient space to its closest neighbors. Safe clickable space has a diameter of 16px instead of at least 24px.", "html": "<a href=\"mailto:<EMAIL>\" class=\"social-icon-modern\" aria-label=\"Email\" tabindex=\"0\"><svg viewBox=\"0 0 24 24\" class=\"w-5 h-5\"><path fill=\"currentColor\" d=\"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z\"></path></svg></a>", "impact": "serious", "none": [], "target": ["a[aria-label=\"Email\"]"]}], "tags": ["cat.sensory-and-visual-cues", "wcag22aa", "wcag258"]}]
    at /Users/<USER>/Developer/app/tests/a11y.spec.ts:67:49
```

# Page snapshot

```yaml
- button "Switch to dark mode"
- heading "Nathan Dryer" [level=1]
- paragraph: AI Product Manager - Agent Orchestrator
- text: Search or type a command...
- button "Scroll to timeline"
- main:
  - heading "Professional Timeline" [level=2]
  - heading "Skills & Expertise" [level=2]
  - paragraph: Professional competencies across AI, product, and technical domains
  - button "All Skills"
  - button "AI/ML & Product"
  - button "Technical Skills"
  - button "Leadership & Strategy"
  - heading "LLM Orchestration & Prompt Engineering" [level=3]
  - text: Expert Proficiency 95% AI/ML & Product
  - heading "AI/ML Product Development" [level=3]
  - text: Expert Proficiency 92% AI/ML & Product
  - heading "Agent Architecture Design" [level=3]
  - text: Expert Proficiency 90% AI/ML & Product
  - heading "Natural Language Interface Design" [level=3]
  - text: Advanced Proficiency 88% Technical Skills
  - heading "Product Strategy & Roadmapping" [level=3]
  - text: Expert Proficiency 94% AI/ML & Product
  - heading "User Experience Research" [level=3]
  - text: Advanced Proficiency 85% Technical Skills
  - heading "Technical Requirement Definition" [level=3]
  - text: Advanced Proficiency 87% Technical Skills
  - heading "Data Analysis & Metrics" [level=3]
  - text: Advanced Proficiency 82% Technical Skills
  - heading "Agile Product Management" [level=3]
  - text: Expert Proficiency 90% AI/ML & Product
  - heading "Cross-functional Team Leadership" [level=3]
  - text: Advanced Proficiency 88% Leadership & Strategy
  - heading "Vector Database Implementation" [level=3]
  - text: Intermediate Proficiency 78% AI/ML & Product
  - heading "Multimodal AI Applications" [level=3]
  - text: Advanced Proficiency 84% AI/ML & Product
  - heading "Retrieval Augmented Generation" [level=3]
  - text: Advanced Proficiency 86% Technical Skills
  - heading "Python & TypeScript Development" [level=3]
  - text: Intermediate Proficiency 75% Technical Skills
  - heading "Prototyping & Wireframing" [level=3]
  - text: Advanced Proficiency 80% Technical Skills
  - heading "AI Ethics & Responsible AI" [level=3]
  - text: Advanced Proficiency 89% AI/ML & Product
  - heading "Product Analytics & KPIs" [level=3]
  - text: Expert Proficiency 91% AI/ML & Product
  - heading "Go-to-Market Strategy" [level=3]
  - text: Advanced Proficiency 83% AI/ML & Product
  - heading "Competitive Analysis" [level=3]
  - text: Intermediate Proficiency 79% Technical Skills
  - heading "User Story Mapping" [level=3]
  - text: Advanced Proficiency 86% Technical Skills
- contentinfo:
  - link "GitHub":
    - /url: https://github.com/nathan-dryer
    - img
  - link "LinkedIn":
    - /url: https://www.linkedin.com/in/nathandryer
    - img
  - link "Email":
    - /url: mailto:<EMAIL>
    - img
  - paragraph: © 2025 Nathan Dryer. All rights reserved.
- text: Press ⌘+K for commands
- button "Open command palette"
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import AxeBuilder from '@axe-core/playwright';
   3 |
   4 | test.describe('Accessibility Tests', () => {
   5 |   test('should not have any WCAG 2.5.8 violations (touch target size)', async ({ page }) => {
   6 |     // Navigate to the homepage
   7 |     await page.goto('/');
   8 |     
   9 |     // Wait for the page to load completely
  10 |     await page.waitForLoadState('networkidle');
  11 |     
  12 |     // Run axe scan with focus on WCAG 2.5.8 (target size)
  13 |     const accessibilityScanResults = await new AxeBuilder({ page })
  14 |       .withTags(['wcag21aa', 'wcag22aa'])
  15 |       .withRules(['target-size'])
  16 |       .analyze();
  17 |     
  18 |     // Extract violations related to target size (WCAG 2.5.8)
  19 |     const targetSizeViolations = accessibilityScanResults.violations.filter(
  20 |       violation => violation.id === 'target-size' || 
  21 |                    violation.description?.includes('target') ||
  22 |                    violation.description?.includes('44')
  23 |     );
  24 |     
  25 |     // Log violations for debugging
  26 |     if (targetSizeViolations.length > 0) {
  27 |       console.log('WCAG 2.5.8 Target Size Violations Found:');
  28 |       targetSizeViolations.forEach((violation, index) => {
  29 |         console.log(`${index + 1}. ${violation.id}: ${violation.description}`);
  30 |         console.log(`   Impact: ${violation.impact}`);
  31 |         console.log(`   Nodes affected: ${violation.nodes.length}`);
  32 |         violation.nodes.forEach((node, nodeIndex) => {
  33 |           console.log(`   ${nodeIndex + 1}. ${node.html}`);
  34 |           console.log(`      Target: ${node.target.join(', ')}`);
  35 |         });
  36 |       });
  37 |     }
  38 |     
  39 |     // Expect no WCAG 2.5.8 violations
  40 |     expect(targetSizeViolations).toHaveLength(0);
  41 |   });
  42 |
  43 |   test('should not have any accessibility violations', async ({ page }) => {
  44 |     // Navigate to the homepage
  45 |     await page.goto('/');
  46 |     
  47 |     // Wait for the page to load completely
  48 |     await page.waitForLoadState('networkidle');
  49 |     
  50 |     // Run comprehensive axe scan
  51 |     const accessibilityScanResults = await new AxeBuilder({ page })
  52 |       .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
  53 |       .analyze();
  54 |     
  55 |     // Log all violations for debugging
  56 |     if (accessibilityScanResults.violations.length > 0) {
  57 |       console.log('Accessibility Violations Found:');
  58 |       accessibilityScanResults.violations.forEach((violation, index) => {
  59 |         console.log(`${index + 1}. ${violation.id}: ${violation.description}`);
  60 |         console.log(`   Impact: ${violation.impact}`);
  61 |         console.log(`   Help: ${violation.help}`);
  62 |         console.log(`   Nodes affected: ${violation.nodes.length}`);
  63 |       });
  64 |     }
  65 |     
  66 |     // Expect no accessibility violations
> 67 |     expect(accessibilityScanResults.violations).toHaveLength(0);
     |                                                 ^ Error: expect(received).toHaveLength(expected)
  68 |   });
  69 | }); 
```